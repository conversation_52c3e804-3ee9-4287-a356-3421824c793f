"""
Configuration settings using environment variables from .env file.
"""
import os
import logging
from dotenv import load_dotenv
from typing import Dict, Any, Optional

# Load variables from .env file
load_dotenv()


class Config:
    """Simple configuration class that loads settings from environment variables"""

    @classmethod
    def get(cls, key: str, default: Any = None) -> Any:
        """Get a configuration value with optional type conversion"""
        value = os.getenv(key, default)
        
        # Handle boolean values
        if isinstance(default, bool):
            return value.lower() in ('true', 'yes', '1', 't') if value else default
            
        # Handle integer values
        if isinstance(default, int):
            try:
                return int(value) if value else default
            except (ValueError, TypeError):
                return default
                
        return value
    
    @classmethod
    def get_network_path(cls) -> str:
        """Returns the network path for file storage"""
        server = cls.get('SMB_SERVER')
        share = cls.get('SMB_SHARE', 'FV-FileBackups')
        base_path = cls.get('SMB_BASE_PATH', '/archival')
        
        if server:
            return f'//{server}/{share}{base_path}'
        return os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backup_files')
    
    # Application settings
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    DEBUG_MODE = os.getenv('DEBUG_MODE', 'false').lower() in ('true', 'yes', '1', 't')
    MOCK_API = os.getenv('MOCK_API', 'false').lower() in ('true', 'yes', '1', 't')
    
    # Database settings
    DB_ENGINE = os.getenv('DB_ENGINE', '')
    DB_HOST = os.getenv('DB_HOST', '')
    DB_PORT = int(os.getenv('DB_PORT', '3306'))
    DB_NAME = os.getenv('DB_NAME', '')
    DB_USER = os.getenv('DB_USER', '')
    DB_PASSWORD = os.getenv('DB_PASSWORD', '')
    DB_SSL_MODE = os.getenv('DB_SSL_MODE', 'DISABLED')
    
    # Redis settings
    REDIS_URL = os.getenv('REDIS_URL', 'redis://*************:6379/0')
    
    # Filevine API settings
    FILEVINE_API_URL = os.getenv('FILEVINE_API_URL', 'https://api.filevineapp.com')
    FILEVINE_ACCESS_TOKEN = os.getenv('FILEVINE_ACCESS_TOKEN')
    FILEVINE_CLIENT_ID = os.getenv('FILEVINE_CLIENT_ID')
    FILEVINE_CLIENT_SECRET = os.getenv('FILEVINE_CLIENT_SECRET')
    FILEVINE_ORG_ID = os.getenv('FILEVINE_ORG_ID')
    FILEVINE_USER_ID = os.getenv('FILEVINE_USER_ID')
    FILEVINE_SESSION_URL = os.getenv('FILEVINE_SESSION_URL')
    FILEVINE_AUTH_URL = os.getenv('FILEVINE_AUTH_URL')
    
    # SMB Storage settings
    SMB_SERVER = os.getenv('SMB_SERVER')
    SMB_USERNAME = os.getenv('SMB_USERNAME')
    SMB_PASSWORD = os.getenv('SMB_PASSWORD')
    SMB_SHARE = os.getenv('SMB_SHARE', 'FV-FileBackups')
    SMB_BASE_PATH = os.getenv('SMB_BASE_PATH', '/archival')
    
    # API Security
    API_KEY = os.getenv('API_KEY', '')
    
    # Rate limiting
    API_RATE_LIMIT = int(os.getenv('API_RATE_LIMIT', '1000'))
    
    # Timezone configuration for datetime handling
    TIMEZONE = os.getenv('TIMEZONE', 'UTC')

    # Additional helper methods could be added here as needed
