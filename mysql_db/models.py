from sqlalchemy import Column, Inte<PERSON>, <PERSON>, Boolean, DateTime, JSON, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime
from uuid import uuid4

Base = declarative_base()

class IdPairMixin:
    """Mixin for handling Filevine ID pairs (native, partner)."""
    native = Column(Integer, nullable=False)
    partner = Column(String, default="")

class Project(Base):
    __tablename__ = 'projects'
    id = Column(Integer, primary_key=True, autoincrement=True)
    native_id = Column(Integer, nullable=False, unique=True)
    partner_id = Column(String(255), default="")
    name = Column(String(255))
    project_name = Column(String(255))
    client_name = Column(String(255))
    created_date = Column(String(255))
    status = Column(String(100))
    is_archived = Column(Boolean)
    first_primary_name = Column(String(255))
    first_primary_username = Column(String(255))
    project_or_client_name = Column(String(255))
    phase_name = Column(String(255))
    org_id = Column(Integer)
    unique_key = Column(String(255))
    project_url = Column(String(255))
    last_activity = Column(String(255))
    project_email_address = Column(String(255))
    incident_date = Column(String(255))
    number = Column(String(100))
    client_id_native = Column(Integer)
    client_id_partner = Column(String(255))
    project_type_id_native = Column(Integer)
    project_type_id_partner = Column(String(255))
    phase_id_native = Column(Integer)
    phase_id_partner = Column(String(255))
    root_doc_folder_id_native = Column(Integer)
    root_doc_folder_id_partner = Column(String(255))
    hashtags = Column(JSON)
    meta_data = Column(JSON)
    # Relationships
    folders = relationship(
        "Folder",
        back_populates="project",
        foreign_keys="[Folder.project_id_native]"
    )
    documents = relationship(
        "Document",
        back_populates="project",
        foreign_keys="[Document.project_id_native]"
    )

class Folder(Base):
    __tablename__ = 'folders'
    id = Column(Integer, primary_key=True, autoincrement=True)
    native_id = Column(Integer, nullable=False, unique=True)
    partner_id = Column(String(255), default="")
    parent_id_native = Column(Integer)
    parent_id_partner = Column(String(255))
    project_id_native = Column(Integer, ForeignKey('projects.native_id'))
    project_id_partner = Column(String(255))
    name = Column(String(255))
    path = Column(String(1000))
    is_archived = Column(Boolean, default=False)
    is_root_folder = Column(Boolean, default=False)
    created_date = Column(String(255))
    hashtags = Column(JSON)
    meta_data = Column(JSON)
    # Relationships
    project = relationship(
        "Project",
        back_populates="folders",
        foreign_keys=[project_id_native]
    )
    documents = relationship("Document", back_populates="folder")

class Document(Base):
    __tablename__ = 'documents'
    id = Column(Integer, primary_key=True, autoincrement=True)
    native_id = Column(Integer, nullable=False, unique=True)
    partner_id = Column(String(255), default="")
    folder_id_native = Column(Integer, ForeignKey('folders.native_id'))
    folder_id_partner = Column(String(255))
    project_id_native = Column(Integer, ForeignKey('projects.native_id'))
    project_id_partner = Column(String(255))
    name = Column(String(500))
    original_filename = Column(String(500))
    path = Column(String(1000))
    is_archived = Column(Boolean, default=False)
    ocr_data = Column(String, nullable=True)
    upload_date = Column(String(255))
    file_size = Column(Integer)
    content_type = Column(String(100))
    hash = Column(String(255))
    download_url = Column(String(2000))
    download_status = Column(String(50))
    download_started_at = Column(DateTime, nullable=True)
    download_completed_at = Column(DateTime, nullable=True)
    download_error = Column(String)
    download_attempts = Column(Integer, default=0)
    version_key = Column(String(255))
    hashtags = Column(JSON)
    links = Column(JSON)
    # Relationships
    project = relationship("Project", foreign_keys=[project_id_native], back_populates="documents")
    folder = relationship("Folder", foreign_keys=[folder_id_native], back_populates="documents")
    backups = relationship("BackupTracker", back_populates="document")

class BackupTracker(Base):
    __tablename__ = 'backup_tracker'
    id = Column(String, primary_key=True, default=lambda: str(uuid4()))
    document_id = Column(Integer, ForeignKey('documents.id'))
    project_id = Column(Integer, ForeignKey('projects.id'))
    original_filename = Column(String)
    backup_date = Column(DateTime, default=datetime.utcnow)
    file_size = Column(Integer, default=0)
    file_checksum = Column(String, default="")
    file_path = Column(String, default="")
    storage_location = Column(String, default="")
    backup_type = Column(String, default="")  # e.g., 'local', 's3'
    content_type = Column(String, default="")
    checksum_verified = Column(Boolean, default=False)
    verification_date = Column(DateTime)
    backup_status = Column(String, default="")  # e.g., 'completed', 'failed'
    error_message = Column(String, default="")
    meta_data = Column(JSON, default=lambda: {})  # Renamed from metadata which is a reserved name in SQLAlchemy
    # Relationships
    document = relationship("Document", back_populates="backups")
    project = relationship("Project")

class LogEntry(Base):
    __tablename__ = 'logs'
    id = Column(Integer, primary_key=True, autoincrement=True)
    timestamp = Column(DateTime, default=datetime.utcnow)
    level = Column(String(20))
    message = Column(String)
    project_id = Column(Integer, ForeignKey('projects.id'), nullable=True)
    context = Column(JSON)
    # Relationships
    project = relationship("Project")