"""
Field mappings and ID extraction utilities for Filevine API data processing.

This module contains the field mapping configurations and utility functions
for converting Filevine API response data to database-compatible formats.
"""

from typing import Dict, Any, Union


def extract_native_id(x: Union[Dict[str, Any], Any]) -> Any:
    """
    Extract native ID from Filevine ID objects.
    
    Args:
        x: Either a dict with 'native' key or a direct value
        
    Returns:
        The native ID value or the original value if not a dict
    """
    if isinstance(x, dict) and 'native' in x:
        return x['native']
    return x


class ProjectMappings:
    """Field mappings for projects table."""
    
    @staticmethod
    def get_field_mappings() -> Dict[str, str]:
        """Get API field to database column mappings for projects."""
        return {
            'projectId': 'project_id_native',
            'projectName': 'project_name', 
            'clientName': 'client_name',
            'createdDate': 'created_date',
            'isArchived': 'is_archived',
            'firstPrimaryName': 'first_primary_name',
            'firstPrimaryUsername': 'first_primary_username',
            'projectOrClientName': 'project_or_client_name',
            'phaseName': 'phase_name',
            'orgId': 'org_id',
            'uniqueKey': 'unique_key',
            'projectUrl': 'project_url',
            'lastActivity': 'last_activity',
            'projectEmailAddress': 'project_email_address',
            'incidentDate': 'incident_date',
            'number': 'number',
            'clientId': 'client_id_native',
            'projectTypeId': 'project_type_id_native',
            'phaseId': 'phase_id_native',
            'rootDocFolderId': 'root_doc_folder_id_native',
            'hashtags': 'hashtags',
            'metadata': 'metadata',
            'projectTypeCode': 'status'
        }
    
    @staticmethod
    def get_primary_id_field() -> str:
        """Get the primary ID field name for projects."""
        return 'projectId'


class FolderMappings:
    """Field mappings for folders table."""
    
    @staticmethod
    def get_field_mappings() -> Dict[str, str]:
        """Get API field to database column mappings for folders."""
        return {
            'name': 'name',         # For standard API responses
            'displayName': 'name',  # For display name
            'isArchived': 'is_archived',
            'createdDate': 'created_date',
            'path': 'path',
            'isRootFolder': 'is_root_folder',
            'metadata': 'meta_data'
        }
    
    @staticmethod
    def get_primary_id_field() -> str:
        """Get the primary ID field name for folders."""
        return 'folderId'
    
    @staticmethod
    def get_foreign_key_mappings() -> Dict[str, str]:
        """Get foreign key field mappings for folders."""
        return {
            'projectId': 'project_id_native',
            'parentId': 'parent_id_native'
        }


class DocumentMappings:
    """Field mappings for documents table."""
    
    @staticmethod
    def get_field_mappings() -> Dict[str, str]:
        """Get API field to database column mappings for documents."""
        return {
            'filename': 'name',
            'originalFilename': 'original_filename',
            'size': 'file_size',
            'contentType': 'content_type',
            'uploadDate': 'upload_date',
            'isArchived': 'is_archived',
            'path': 'path',
            'downloadUrl': 'download_url',
            'version': 'version_key',
            'ocrData': 'ocr_data',
            'metadata': 'meta_data'
        }
    
    @staticmethod
    def get_primary_id_field() -> str:
        """Get the primary ID field name for documents."""
        return 'documentId'
    
    @staticmethod
    def get_foreign_key_mappings() -> Dict[str, str]:
        """Get foreign key field mappings for documents."""
        return {
            'projectId': 'project_id_native',
            'folderId': 'folder_id_native',
            'uploaderId': 'uploader_id_native'
        }


class CommonMappings:
    """Common field mappings used across all table types."""
    
    @staticmethod
    def get_common_id_fields() -> list:
        """Get list of common ID fields that need native extraction."""
        return ['clientId', 'projectTypeId', 'phaseId', 'rootDocFolderId']
    
    @staticmethod
    def convert_id_field_name(field: str) -> str:
        """Convert ID field name to database column name."""
        return field.replace('Id', '_id_native')


def get_table_mappings(table_type: str) -> Dict[str, Any]:
    """
    Get the appropriate mapping class for a table type.
    
    Args:
        table_type: Type of table ('projects', 'folders', 'documents')
        
    Returns:
        Dictionary with mapping methods for the table type
    """
    mapping_classes = {
        'projects': ProjectMappings,
        'folders': FolderMappings,
        'documents': DocumentMappings
    }
    
    if table_type not in mapping_classes:
        raise ValueError(f"Unknown table type: {table_type}")
    
    mapping_class = mapping_classes[table_type]
    
    result = {
        'field_mappings': mapping_class.get_field_mappings(),
        'primary_id_field': mapping_class.get_primary_id_field()
    }
    
    # Add foreign key mappings if available
    if hasattr(mapping_class, 'get_foreign_key_mappings'):
        result['foreign_key_mappings'] = mapping_class.get_foreign_key_mappings()
    
    return result
