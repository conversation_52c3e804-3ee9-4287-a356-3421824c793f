<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Filevine-MySQL Dashboard</title>
    <link rel="stylesheet" href="/static/dark.css">
</head>
<body>
    <div class="container">
        <h1>Filevine-MySQL Dashboard</h1>
        <section>
            <h2>Run Task</h2>
            <form id="task-form">
                <select id="task-select">
                    <option value="update_projects">Project Update</option>
                    <option value="update_folders">Folder Update</option>
                    <option value="update_documents">Document Update</option>
                </select>
                <button type="submit">Run</button>
            </form>
        </section>
        <section>
            <h2>Live Activity Monitor</h2>
            <ul id="activity-log"></ul>
        </section>
        <section>
            <h2>Log Viewer</h2>
            <pre id="log-viewer"></pre>
        </section>
    </div>
    <script>
        // WebSocket for activity monitor
        const activityLog = document.getElementById('activity-log');
        const ws = new WebSocket(`ws://${window.location.host}/ws/activity`);
        ws.onmessage = (event) => {
            const li = document.createElement('li');
            li.textContent = event.data;
            activityLog.appendChild(li);
            // Also update log viewer
            document.getElementById('log-viewer').textContent += event.data + '\n';
        };

        // Task form
        document.getElementById('task-form').onsubmit = async (e) => {
            e.preventDefault();
            const task = document.getElementById('task-select').value;
            await fetch('/api/run-task', {
                method: 'POST',
                headers: {'Content-Type': 'application/json'},
                body: JSON.stringify({task})
            });
        };
    </script>
</body>
</html>
