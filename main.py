from fastapi import <PERSON><PERSON><PERSON>, Request, WebSocket, WebSocketDisconnect
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles
from starlette.websockets import WebSocketState
import logging
import asyncio
import os
from utils import get_logger

app = FastAPI()

# Mount static files for CSS/JS
app.mount("/static", StaticFiles(directory="static"), name="static")

# Set up templates directory
TEMPLATES_DIR = os.path.join(os.path.dirname(__file__), "templates")
templates = Jinja2Templates(directory=TEMPLATES_DIR)

# In-memory log/activity queue for demo
activity_log = []
activity_subscribers = set()

@app.get("/", response_class=HTMLResponse)
async def dashboard(request: Request):
    return templates.TemplateResponse("index.html", {"request": request})

@app.post("/api/run-task")
async def run_task(request: Request):
    logger = get_logger()
    data = await request.json()
    task_type = data.get("task")
    msg = f"Triggered task: {task_type}"
    logger.info(msg)
    try:
        from filevine import FV_API
        from mysql_db import FV_db, models
        
        # Create session for Filevine API
        fv_session = FV_API.FilevineSession()
        
        if task_type == "update_projects":
            # For projects, we can still use the direct approach since there are fewer of them
            # But use the correct key field (native_id, not projectId_native)
            projects_df = await FV_API.getAllProjects(fv_session)
            FV_db.initialize_db_table(projects_df, models.Project, key_cols=["native_id"])
            msg = f"Updated {len(projects_df)} projects from Filevine."
        elif task_type == "update_folders":
            # Use the batch upsert method for folders instead of trying to get all at once
            batch_size = 1000  
            success, last_offset = await FV_db.update_folders(fv_session, batch_size=batch_size)
            if success:
                msg = f"Successfully updated folders from Filevine. Last offset: {last_offset}"
            else:
                msg = f"Folder update incomplete. Last successful offset: {last_offset}"
        elif task_type == "update_documents":
            # Use the batch upsert method for documents instead of trying to get all at once
            batch_size = 1000  
            success, resume_data = await FV_db.update_documents(fv_session, batch_size=batch_size)
            if success:
                msg = f"Successfully updated documents from Filevine."
            else:
                msg = f"Document update incomplete. Resume data available for future runs."
        else:
            msg = f"Unknown task: {task_type}"
        activity_log.append(msg)
        await notify_activity(msg)
        return {"status": "ok", "message": msg}
    except Exception as e:
        err_msg = f"Task {task_type} failed: {e}"
        logger.error(f"Error in task execution: {err_msg}", exc_info=True)
        activity_log.append(err_msg)
        await notify_activity(err_msg)
        return {"status": "error", "message": err_msg}

@app.websocket("/ws/activity")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    activity_subscribers.add(websocket)
    try:
        # Send recent log
        for msg in activity_log[-20:]:
            await websocket.send_text(msg)
        while True:
            # Keep alive
            await asyncio.sleep(10)
            if websocket.application_state != WebSocketState.CONNECTED:
                break
    except WebSocketDisconnect:
        pass
    finally:
        activity_subscribers.discard(websocket)

def log_activity(msg: str):
    activity_log.append(msg)
    asyncio.create_task(notify_activity(msg))

async def notify_activity(msg: str):
    for ws in set(activity_subscribers):
        try:
            await ws.send_text(msg)
        except Exception:
            activity_subscribers.discard(ws)
