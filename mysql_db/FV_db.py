import json
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from sqlalchemy.exc import IntegrityError
from sqlalchemy import func
import os
from utils import get_logger
from datetime import datetime
from config import Config
from app import db
from mysql_db.models import Project, Folder, Document, BackupTracker, LogEntry  # Updated to use SQLAlchemy models

# These imports are at the module level but are actually imported at the function level
# to avoid circular imports
FilevineSession = Any  # Type hint placeholder
getAllFolders = None
getAllDocuments = None
FolderPaginator = None
FilePaginator = None

def _init_imports():
    """Initialize imports that would cause circular imports at module level"""
    global FilevineSession, getAllFolders, getAllDocuments, FolderPaginator, FilePaginator
    from filevine.FV_API import FilevineSession, getAllFolders, getAllDocuments, FolderPaginator, FilePaginator

async def results_to_df(response_or_data: Any, force_table_type: Optional[str] = None) -> pd.DataFrame:
    """
    Convert API response or parsed data to DataFrame for database insertion.

    Args:
        response_or_data: Response object from requests/aiohttp OR pre-parsed data dictionary
        force_table_type: Force a specific table type instead of auto-detection

    Returns:
        Processed pandas DataFrame
    """
    # Import from data processor module
    from mysql_db.data_processor import results_to_df as process_results
    return await process_results(response_or_data, force_table_type)

# Import table operations from separate module
from mysql_db.table_operations import initialize_db_table, append_after_latest, insert_unique

class DocumentQueryForDownload:
    def __init__(self, project_id: str):
        self.project_id = project_id
        
    def query_files(self) -> pd.DataFrame:
        """
        Query files for a project, returning DataFrame for download.
        """
        files = Document.query.filter_by(project_id=self.project_id, backup_complete=0).all()
        if not files:
            return pd.DataFrame()

        data = []
        for f in files:
            folder = Folder.query.get(f.folder_id) if f.folder_id else None
            project = Project.query.get(f.project_id)
            
            folder_path = ""
            if folder:
                folder_path = folder.name
                current_folder = folder
                while current_folder.parentId_native:
                    parent = Folder.query.filter_by(folderId_native=current_folder.parentId_native).first()
                    if parent:
                        folder_path = os.path.join(parent.name, folder_path)
                    current_folder = parent
            
            file_type = os.path.splitext(f.filename)[1].lower() if f.filename else ""
            
            file_entry = {
                'id': f.id,
                'name': f.filename,
                'file_id_native': f.documentId_native,
                'folder_id': f.folder_id,
                'folder_name': folder.name if folder else "",
                'folder_path': folder_path,
                'project_name': project.projectName if project else "",
                'file_size': f.size,
                'file_type': file_type,
                'original_created_date': None,  # Populated externally
                'original_modified_date': None,
                'backup_complete': f.backup_complete,
                'upload_date': f.uploadDate,
                'uploader_id': f.uploaderId,
                'hashtags': json.dumps(f.hashtags),
                'source_system': "Filevine",
                'backup_location': os.path.join(Config.get_network_path(), str(self.project_id), folder_path),
                'backup_method': "incremental",
                'compression_status': True,
                'encryption_status': True,
                'retention_policy': "30_days",
                'rpo_compliance': False,
                'rto_estimate': 30,
                'backup_dependencies': json.dumps([])
            }
            data.append(file_entry)
        
        return pd.DataFrame(data)
    
    def update_backup_status(self, file_id: str, success: bool, error_message: str = None, **kwargs) -> None:
        """
        Update backup status in backup_tracker table.
        """


# Import folders operations from separate module
from mysql_db.folders import update_folders


async def update_documents(session: Any, project_ids: Optional[List[int]] = None, batch_size: int = 1000, max_records: Optional[int] = None, resume_data: Optional[Dict[str, int]] = None) -> Tuple[bool, Dict[str, int]]:
    """
    Update documents table with data from Filevine API with batch upsert for reliability.
    Can update documents for specific projects or all projects.
    Implements batch processing with database commits after each batch to prevent data loss.
    
    Args:
        session: Authenticated FilevineSession
        project_ids: List of project IDs to fetch documents for (None for all projects)
        batch_size: Number of records to process in each batch
        max_records: Maximum number of records to process per project (None for all)
        resume_data: Dictionary mapping project_id to last successful offset for resuming
        
    Returns:
        Tuple containing (success status, dictionary of project_id -> last successful offset)
    """
    # Initialize imports to avoid circular imports
    _init_imports()
    
    logger = get_logger()
    logger.info(f"Starting document update task with batch size {batch_size}")
    
    try:
        if project_ids is None:
            # Get all project IDs if not specified
            # Use db_session instead of Project.query which may not be available in standalone scripts
            from app import db_session
            projects = db_session.query(Project).with_entities(Project.native_id).all()
            project_ids = [p[0] for p in projects]
            logger.info(f"Found {len(project_ids)} projects to process")
        
        # Initialize resume data if not provided
        if resume_data is None:
            resume_data = {}
            
        # Track statistics for reporting
        total_documents = 0
        successful_projects = 0
        last_success_offsets = {}
        
        for project_id in project_ids:
            # Initialize tracking for this project
            current_offset = resume_data.get(str(project_id), 0)
            project_total_docs = 0
            last_success_offset = current_offset
            
            try:
                logger.info(f"Processing documents for project {project_id} starting at offset {current_offset}")
                
                # Create a paginator for this project that will handle batch retrieval
                paginator = FilePaginator(session, offset=str(current_offset), limit=str(batch_size), projectID=str(project_id))
                
                # Process documents in batches for this project
                async for doc_batch_df in paginator.async_paginate(paginator.async_fetch, max_records, return_chunks=True):
                    if doc_batch_df.empty:
                        logger.warning(f"Empty document batch for project {project_id} at offset {current_offset}, stopping")
                        break
                    
                    batch_size_actual = len(doc_batch_df)
                    logger.info(f"Processing batch of {batch_size_actual} documents for project {project_id} at offset {current_offset}")
                    
                    try:
                        # No need to set project_id as foreign key since we now use native_id/partner_id pattern
                        # Also, the folder_id_native extraction is now handled in results_to_df
                        
                        # Verify that folder_id_native is properly set
                        if 'folder_id_native' not in doc_batch_df.columns or doc_batch_df['folder_id_native'].isna().all():
                            logger.warning("No folder_id_native found in document data after processing, DB constraint may fail")
                        
                        # Update documents table with this batch
                        success = initialize_db_table(doc_batch_df, Document, ['native_id'])
                        
                        if success:
                            # Update statistics and last successful offset
                            project_total_docs += batch_size_actual
                            last_success_offset = current_offset
                            logger.info(f"Successfully upserted {batch_size_actual} documents for project {project_id} at offset {current_offset}")
                        else:
                            logger.error(f"Failed to upsert documents batch for project {project_id} at offset {current_offset}")
                            # Log the failure for tracking
                            log_entry = LogEntry(
                                timestamp=datetime.utcnow(),
                                level="ERROR",
                                message=f"Failed to upsert documents batch",
                                context={
                                    "project_id": project_id,
                                    "offset": current_offset,
                                    "batch_size": batch_size_actual,
                                    "task": "update_documents"
                                }
                            )
                            db.session.add(log_entry)
                            db.session.commit()
                    except Exception as batch_error:
                        logger.error(f"Error processing documents batch for project {project_id} at offset {current_offset}: {batch_error}")
                        # Log the exception
                        log_entry = LogEntry(
                            timestamp=datetime.utcnow(),
                            level="ERROR",
                            message=f"Exception processing documents batch: {str(batch_error)}",
                            context={
                                "project_id": project_id,
                                "offset": current_offset,
                                "batch_size": batch_size_actual,
                                "task": "update_documents",
                                "exception": str(batch_error)
                            }
                        )
                        db.session.add(log_entry)
                        db.session.commit()
                    
                    # Clear the DataFrame to free memory before processing next batch
                    del doc_batch_df
                    
                    # Update offset for next iteration
                    current_offset += batch_size_actual
                
                # Update project statistics
                if project_total_docs > 0:
                    successful_projects += 1
                    total_documents += project_total_docs
                    last_success_offsets[str(project_id)] = last_success_offset
                    logger.info(f"Completed documents update for project {project_id}: processed {project_total_docs} documents")
            
            except Exception as project_error:
                logger.error(f"Error processing project {project_id}: {project_error}")
                # Log the exception for this project
                log_entry = LogEntry(
                    timestamp=datetime.utcnow(),
                    level="ERROR",
                    message=f"Exception processing documents for project: {str(project_error)}",
                    context={
                        "project_id": project_id,
                        "last_success_offset": last_success_offset,
                        "task": "update_documents",
                        "exception": str(project_error)
                    }
                )
                db.session.add(log_entry)
                db.session.commit()
                
                # Still track successful batches for this project
                if last_success_offset > 0:
                    last_success_offsets[str(project_id)] = last_success_offset
        
        # Final report
        logger.info(f"Document update completed: processed {total_documents} documents across {successful_projects} projects")
        
        # Return success status and the last successful offsets for each project
        return successful_projects > 0, last_success_offsets
    
    except Exception as e:
        logger.error(f"Error updating documents: {e}")
        # Log the failure for tracking/querying on next run
        log_entry = LogEntry(
            timestamp=datetime.utcnow(),
            level="ERROR",
            message=f"Failed to complete document update task: {str(e)}",
            context={
                "task": "update_documents",
                "exception": str(e)
            }
        )
        try:
            db.session.add(log_entry)
            db.session.commit()
        except Exception as log_error:
            logger.error(f"Could not log document update failure: {log_error}")
            
        return False, resume_data or {}


def update_backup_status(self, file_id: str, success: bool, error_message: str = None, **kwargs) -> None:
    """
    Update backup status in backup_tracker table.
    """
    try:
        file = Document.query.get(file_id)
        if not file:
            logger = get_logger()
            logger.error(f"File {file_id} not found.")
            return

            tracker = BackupTracker(
                id=str(uuid4()),
                document_id=file_id,
                project_id=self.project_id,
                original_filename=kwargs.get('original_filename', file.filename),
                backup_date=datetime.utcnow(),
                file_size=kwargs.get('file_size', 0),
                file_checksum=kwargs.get('file_checksum', ''),
                file_path=kwargs.get('file_path', ''),
                storage_location=kwargs.get('storage_location', ''),
                backup_type=kwargs.get('backup_type', 'local'),
                content_type=kwargs.get('content_type', ''),
                checksum_verified=kwargs.get('checksum_verified', False),
                verification_date=kwargs.get('verification_date'),
                backup_status='completed' if success else 'failed',
                error_message=error_message or '',
                meta_data=kwargs.get('meta_data', {})  # Using renamed column
            )
            file.backup_complete = 1 if success else 0
            db.session.add(tracker)
            db.session.commit()
    except Exception as e:
        db.session.rollback()
        logger = get_logger()
        logger.error(f"Backup status update error: {e}")