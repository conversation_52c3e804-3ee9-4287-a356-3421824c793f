import json
import pandas as pd
from typing import Dict, List, Optional, Union, Any, Tuple
from sqlalchemy.exc import IntegrityError
from sqlalchemy import func
import os
from utils import get_logger
from datetime import datetime
from config import Config
from app import db
from mysql_db.models import Project, Folder, Document, BackupTracker, LogEntry  # Updated to use SQLAlchemy models

# These imports are at the module level but are actually imported at the function level
# to avoid circular imports
FilevineSession = Any  # Type hint placeholder
getAllFolders = None
getAllDocuments = None
FolderPaginator = None
FilePaginator = None

def _init_imports():
    """Initialize imports that would cause circular imports at module level"""
    global FilevineSession, getAllFolders, getAllDocuments, FolderPaginator, FilePaginator
    from filevine.FV_API import FilevineSession, getAllFolders, getAllDocuments, FolderPaginator, FilePaginator

async def results_to_df(response_or_data: Any, force_table_type: Optional[str] = None) -> pd.DataFrame:
    """
    Convert API response or parsed data to DataFrame for database insertion.
    
    Args:
        response_or_data: Response object from requests/aiohttp OR pre-parsed data dictionary
        
    Returns:
        Processed pandas DataFrame
    """
    logger = get_logger()
    if not response_or_data:
        logger.warning("Empty response or data received in results_to_df")
        return pd.DataFrame()
    
    # Determine if we're working with a response object or a pre-parsed data dictionary
    if isinstance(response_or_data, dict):
        # Already a parsed data dictionary
        data = response_or_data
        logger.debug("Using pre-parsed data dictionary")
    else:
        # Process as a response object
        try:
            # Handle different response types
            if hasattr(response_or_data, 'json') and callable(response_or_data.json):
                # requests Response object
                data = response_or_data.json()
                logger.debug("Parsed JSON from requests Response object")
            elif hasattr(response_or_data, '_body') and response_or_data._body:
                # aiohttp ClientResponse with cached body
                try:
                    data = json.loads(response_or_data._body)
                    logger.debug(f"Parsed response body from cached content: {len(response_or_data._body)} bytes")
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to parse cached response body: {e}")
                    return pd.DataFrame()
            else:
                # Standard aiohttp ClientResponse
                try:
                    data = await response_or_data.json()
                    logger.debug("Parsed JSON from aiohttp ClientResponse")
                except Exception as e:
                    logger.error(f"Failed to decode JSON from response: {e}")
                    # Try reading text as a fallback
                    try:
                        text = await response_or_data.text()
                        logger.debug(f"Response text content length: {len(text)}")
                        data = json.loads(text)
                    except Exception as text_e:
                        logger.error(f"Failed to read response text: {text_e}")
                        return pd.DataFrame()
        except Exception as e:
            logger.error(f"JSON decode error: {e}")
            return pd.DataFrame()
    
    # Validate response format
    if not isinstance(data, dict):
        logger.error(f"Unexpected response format, expected dict got {type(data)}")
        return pd.DataFrame()
        
    if 'items' not in data:
        logger.warning(f"No 'items' found in response data. Keys: {data.keys() if isinstance(data, dict) else 'N/A'}")
        return pd.DataFrame()
        
    df = pd.DataFrame(data['items'])
    
    # Get the table we're dealing with based on forced value or columns in the dataframe
    table_type = force_table_type
    
    # Only auto-detect if no table type was forced
    if table_type is None:
        if 'projectId' in df.columns:
            table_type = 'projects'
        elif 'folderId' in df.columns:
            table_type = 'folders'
        elif 'documentId' in df.columns:
            table_type = 'documents'
    
    logger.info(f"Processing data for table type: {table_type} {'(forced)' if force_table_type else '(auto-detected)'}")
    
    # Validate that we have a valid table type
    if table_type not in ['projects', 'folders', 'documents']:
        logger.error(f"Invalid table type: {table_type}. Unable to map fields properly.")
        return pd.DataFrame()
    
    # Context-aware ID mappings based on table type
    # These mappings reflect the database schema and entity relationships
    
    # Common mappings for all tables - basic ID extraction function
    def extract_native_id(x):
        if isinstance(x, dict) and 'native' in x:
            return x['native']
        return x
    
    # Process primary ID field based on table type
    primary_id_field = None
    if table_type == 'projects' and 'projectId' in df.columns:
        primary_id_field = 'projectId'
        df['native_id'] = df[primary_id_field].apply(extract_native_id)
        df = df.drop(columns=[primary_id_field])
    elif table_type == 'folders' and 'folderId' in df.columns:
        primary_id_field = 'folderId'
        df['native_id'] = df[primary_id_field].apply(extract_native_id)
        df = df.drop(columns=[primary_id_field])
    elif table_type == 'documents' and 'documentId' in df.columns:
        primary_id_field = 'documentId'
        df['native_id'] = df[primary_id_field].apply(extract_native_id)
        df = df.drop(columns=[primary_id_field])
    
    # Handle foreign key relationships based on table type
    if table_type == 'folders':
        # Folders have projectId as foreign key
        if 'projectId' in df.columns:
            df['project_id_native'] = df['projectId'].apply(extract_native_id)
            df = df.drop(columns=['projectId'])
            logger.info(f"Mapped projectId to project_id_native for {len(df)} folders")
            
        # parentId is a self-reference in folders
        if 'parentId' in df.columns:
            df['parent_id_native'] = df['parentId'].apply(extract_native_id)
            df = df.drop(columns=['parentId'])
    
    elif table_type == 'documents':
        # Documents have projectId and folderId as foreign keys
        if not df.empty:
            # Log raw document data to understand structure
            logger.info(f"First document raw data sample: {json.dumps(data['items'][0] if isinstance(data, dict) and 'items' in data else data[0], indent=2)[:500]}")
            logger.info(f"Available document columns: {df.columns.tolist()}")
        
        # Try standard projectId mapping
        if 'projectId' in df.columns:
            try:
                df['project_id_native'] = df['projectId'].apply(extract_native_id)
                df = df.drop(columns=['projectId'])
                logger.info(f"Mapped projectId to project_id_native for {len(df)} documents")
            except Exception as e:
                logger.error(f"Error mapping projectId to project_id_native: {e}")
        # Try alternative locations for projectId
        elif 'links' in df.columns and not df.empty:
            try:
                # Try to extract project ID from links.project URL if available
                if isinstance(df.iloc[0]['links'], dict) and 'project' in df.iloc[0]['links']:
                    logger.info("Extracting project_id_native from links.project URL")
                    df['project_id_native'] = df['links'].apply(
                        lambda x: int(x.get('project', '').split('/')[-1]) if isinstance(x, dict) and 'project' in x else None
                    )
                    logger.info(f"Extracted project_id_native from links for {len(df[df['project_id_native'].notnull()])} documents")
            except Exception as e:
                logger.error(f"Error extracting project_id_native from links: {e}")
                
        # Check if we have project_id_native after all attempts
        if 'project_id_native' not in df.columns or df['project_id_native'].isnull().all():
            logger.error("Failed to extract project_id_native for documents. Foreign key constraint will fail.")
            
        # Handle folderId mapping
        if 'folderId' in df.columns:
            try:
                df['folder_id_native'] = df['folderId'].apply(extract_native_id)
                df = df.drop(columns=['folderId'])
                logger.info(f"Mapped folderId to folder_id_native for {len(df)} documents")
            except Exception as e:
                logger.error(f"Error mapping folderId to folder_id_native: {e}")
        elif 'links' in df.columns and not df.empty:
            try:
                # Try to extract folder ID from links.parent URL if available
                if isinstance(df.iloc[0]['links'], dict) and 'parent' in df.iloc[0]['links']:
                    logger.info("Extracting folder_id_native from links.parent URL")
                    df['folder_id_native'] = df['links'].apply(
                        lambda x: int(x.get('parent', '').split('/')[-1]) if isinstance(x, dict) and 'parent' in x else None
                    )
                    logger.info(f"Extracted folder_id_native from links for {len(df[df['folder_id_native'].notnull()])} documents")
            except Exception as e:
                logger.error(f"Error extracting folder_id_native from links: {e}")
            
        # Handle uploaderId mapping
        if 'uploaderId' in df.columns:
            try:
                df['uploader_id_native'] = df['uploaderId'].apply(extract_native_id)
                df = df.drop(columns=['uploaderId'])
            except Exception as e:
                logger.error(f"Error mapping uploaderId to uploader_id_native: {e}")
                
        # Final check for required foreign keys
        if 'project_id_native' not in df.columns or df['project_id_native'].isnull().all():
            logger.warning("No project_id_native found in documents data, foreign key constraint will fail")
        if 'folder_id_native' not in df.columns or df['folder_id_native'].isnull().all():
            logger.warning("No folder_id_native found in documents data, foreign key constraint will fail")
    
    # Handle common metadata fields that all tables might have
    for field in ['clientId', 'projectTypeId', 'phaseId', 'rootDocFolderId']:
        if field in df.columns:
            target_field = field.replace('Id', '_id_native')
            df[target_field] = df[field].apply(extract_native_id)
            df = df.drop(columns=[field])
    
    # Map fields from API names to database column names
    if table_type == 'projects':
        field_mappings = {
            'projectId': 'project_id_native',
            'projectName': 'project_name', 
            'clientName': 'client_name',
            'createdDate': 'created_date',
            'isArchived': 'is_archived',
            'firstPrimaryName': 'first_primary_name',
            'firstPrimaryUsername': 'first_primary_username',
            'projectOrClientName': 'project_or_client_name',
            'phaseName': 'phase_name',
            'orgId': 'org_id',
            'uniqueKey': 'unique_key',
            'projectUrl': 'project_url',
            'lastActivity': 'last_activity',
            'projectEmailAddress': 'project_email_address',
            'incidentDate': 'incident_date',
            'number': 'number',
            'clientId': 'client_id_native',
            'projectTypeId': 'project_type_id_native',
            'phaseId': 'phase_id_native',
            'rootDocFolderId': 'root_doc_folder_id_native',
            'hashtags': 'hashtags',
            'metadata': 'metadata',
            'projectTypeCode': 'status'
        }
    elif table_type == 'folders':
        # Debug raw folder data to find name field
        if not df.empty:
            logger.info(f"First folder raw data sample: {json.dumps(data['items'][0] if isinstance(data, dict) and 'items' in data else data[0], indent=2)[:500]}")
            logger.info(f"Available folder columns: {df.columns.tolist()}")
            
            # Ensure the name column is present
            if 'name' in df.columns:
                logger.info(f"Found name in folders: {df['name'].head(3).tolist()}")
                # Make sure name gets directly mapped
                df['name'] = df['name']
            else:
                logger.warning("'name' column not found in folder data, will try to create it")
                # If name isn't in columns, look in potential nested objects
                if 'folderId' in df.columns and df.iloc[0]['folderId'] is not None and isinstance(df.iloc[0]['folderId'], dict):
                    # Try to extract name from raw data and add it to DataFrame
                    try:
                        if isinstance(data, dict) and 'items' in data:
                            # Extract names from raw data
                            names = [item.get('name', '') for item in data['items']]
                            df['name'] = names
                            logger.info(f"Extracted folder names from raw data: {names[:3]}")
                        elif isinstance(data, list):
                            names = [item.get('name', '') for item in data]
                            df['name'] = names
                            logger.info(f"Extracted folder names from raw data list: {names[:3]}")
                    except Exception as e:
                        logger.error(f"Error extracting folder names: {e}")
        
        field_mappings = {
            'name': 'name',         # For standard API responses
            'displayName': 'name',  # For display name
            'isArchived': 'is_archived',
            'createdDate': 'created_date',
            'path': 'path',
            'isRootFolder': 'is_root_folder',
            'metadata': 'meta_data'
        }
        
        # We've already handled projectId in the context-specific mapping above
        # This is just a final check to ensure project_id_native is available
        if 'project_id_native' not in df.columns:
            logger.warning("No project_id_native found in folders data, foreign key constraint will fail")
            
    elif table_type == 'documents':
        field_mappings = {
            'filename': 'name',
            'originalFilename': 'original_filename',
            'size': 'file_size',
            'contentType': 'content_type',
            'uploadDate': 'upload_date',
            'isArchived': 'is_archived',
            'path': 'path',
            'downloadUrl': 'download_url',
            'version': 'version_key',
            'ocrData': 'ocr_data',
            'metadata': 'meta_data'
        }
        
        # We've already handled projectId and folderId in the context-specific mapping above
        # This is just a final check to ensure both foreign keys are available
        if 'project_id_native' not in df.columns:
            logger.warning("No project_id_native found in documents data, foreign key constraint will fail")
            
        if 'folder_id_native' not in df.columns:
            logger.warning("No folder_id_native found in documents data, foreign key constraint will fail")
            
        # Links field contains additional relationship data but we don't need it in the database
        # Since we're now using folderId.native directly through our ID mappings above
        if 'links' in df.columns:
            # Just drop the links field as we're getting folder_id_native from folderId.native
            df = df.drop(columns=['links'])
            logger.info("Links field dropped - using folderId.native directly")
        
    else:
        field_mappings = {}
        logger.warning(f"Unknown table type, no field mappings applied. Columns: {df.columns.tolist()}")
    
    # Rename columns based on our field mappings
    for api_field, db_field in field_mappings.items():
        if api_field in df.columns:
            # Only copy to new column name if API field and DB field are different
            if api_field != db_field:
                df[db_field] = df[api_field]
                df = df.drop(columns=[api_field])
            # Otherwise if they're the same name, leave it as is
    
    return df

def initialize_db_table(df: pd.DataFrame, Model: Any, key_cols: List[str]) -> bool:
    """
    Initialize a database table with DataFrame data.
    
    Args:
        df: DataFrame with data
        Model: SQLAlchemy model class (e.g., Project, Folder)
        key_cols: Columns forming unique constraint
        
    Returns:
        Success status
    """
    if df.empty:
        return False

    # Convert dict/list columns to JSON strings and handle NaN values
    for col in df.columns:
        # Convert dict/list to JSON strings
        if df[col].apply(lambda x: isinstance(x, (dict, list))).any():
            df[col] = df[col].apply(json.dumps)
        
        # Replace NaN values with None (will become NULL in SQL)
        if df[col].isna().any():
            df[col] = df[col].where(pd.notna(df[col]), None)

    try:
        # Get model column names once for efficiency
        model_columns = [c.name for c in Model.__table__.columns]
        primary_key_cols = [c.name for c in Model.__table__.primary_key.columns]
        
        # Debug logging for folder name issues
        logger = get_logger()
        if Model.__tablename__ == 'folders':
            logger.info(f"Folder columns in DataFrame: {df.columns.tolist()}")
            if 'name' in df.columns:
                logger.info(f"Sample folder names: {df['name'].head(5).tolist()}")
            else:
                logger.warning(f"'name' column missing from folders DataFrame")
        
        # Process in batches for efficiency
        batch_size = 100
        for i in range(0, len(df), batch_size):
            batch_df = df.iloc[i:i+batch_size]
            for _, row in batch_df.iterrows():
                # Ensure row matches model columns
                row_dict = {k: v for k, v in row.to_dict().items() if k in model_columns}
                
                # Check if record already exists by primary key using key_cols parameter
                filter_dict = {col: row_dict[col] for col in key_cols if col in row_dict}
                if filter_dict:
                    # Try to find the record by key columns
                    existing = db.session.query(Model).filter_by(**filter_dict).first()
                    
                    if existing:
                        # Update existing record with new values
                        for key, value in row_dict.items():
                            setattr(existing, key, value)
                    else:
                        # Create new record
                        record = Model(**row_dict)
                        db.session.add(record)
                else:
                    # No key columns found, just add as new record
                    record = Model(**row_dict)
                    db.session.add(record)
            
            # Commit each batch
            db.session.commit()
            
        return True
    except IntegrityError as e:
        db.session.rollback()
        logger = get_logger()
        logger.error(f"Table init failed: {e}")
        return False

def append_after_latest(df: pd.DataFrame, Model: Any, timestamp_col: str) -> int:
    """
    Append new records based on timestamp.
    
    Args:
        df: DataFrame with data
        Model: SQLAlchemy model
        timestamp_col: Column for timestamp comparison
        
    Returns:
        Number of records added
    """
    if df.empty:
        return 0

    last_timestamp = db.session.query(func.max(getattr(Model, timestamp_col))).scalar()
    df_new = df[df[timestamp_col] > last_timestamp] if last_timestamp else df

    if df_new.empty:
        return 0

    for col in df_new.columns:
        if df_new[col].apply(lambda x: isinstance(x, (dict, list))).any():
            df_new[col] = df_new[col].apply(json.dumps)

    records_added = 0
    try:
        for _, row in df_new.iterrows():
            row_dict = {k: v for k, v in row.to_dict().items() if k in [c.name for c in Model.__table__.columns]}
            record = Model(**row_dict)
            db.session.merge(record)
            records_added += 1
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        logger = get_logger()
        logger.error(f"Append error: {e}")
    return records_added

def insert_unique(df: pd.DataFrame, Model: Any) -> int:
    """
    Insert unique records into table.
    
    Args:
        df: DataFrame with data
        Model: SQLAlchemy model
        
    Returns:
        Number of records added
    """
    if df.empty:
        return 0

    for col in df.columns:
        if df[col].apply(lambda x: isinstance(x, (dict, list))).any():
            df[col] = df[col].apply(json.dumps)

    records_added = 0
    try:
        for _, row in df.iterrows():
            row_dict = {k: v for k, v in row.to_dict().items() if k in [c.name for c in Model.__table__.columns]}
            record = Model(**row_dict)
            db.session.merge(record)
            records_added += 1
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        logger = get_logger()
        logger.error(f"Insert error: {e}")
    return records_added

class DocumentQueryForDownload:
    def __init__(self, project_id: str):
        self.project_id = project_id
        
    def query_files(self) -> pd.DataFrame:
        """
        Query files for a project, returning DataFrame for download.
        """
        files = Document.query.filter_by(project_id=self.project_id, backup_complete=0).all()
        if not files:
            return pd.DataFrame()

        data = []
        for f in files:
            folder = Folder.query.get(f.folder_id) if f.folder_id else None
            project = Project.query.get(f.project_id)
            
            folder_path = ""
            if folder:
                folder_path = folder.name
                current_folder = folder
                while current_folder.parentId_native:
                    parent = Folder.query.filter_by(folderId_native=current_folder.parentId_native).first()
                    if parent:
                        folder_path = os.path.join(parent.name, folder_path)
                    current_folder = parent
            
            file_type = os.path.splitext(f.filename)[1].lower() if f.filename else ""
            
            file_entry = {
                'id': f.id,
                'name': f.filename,
                'file_id_native': f.documentId_native,
                'folder_id': f.folder_id,
                'folder_name': folder.name if folder else "",
                'folder_path': folder_path,
                'project_name': project.projectName if project else "",
                'file_size': f.size,
                'file_type': file_type,
                'original_created_date': None,  # Populated externally
                'original_modified_date': None,
                'backup_complete': f.backup_complete,
                'upload_date': f.uploadDate,
                'uploader_id': f.uploaderId,
                'hashtags': json.dumps(f.hashtags),
                'source_system': "Filevine",
                'backup_location': os.path.join(Config.get_network_path(), str(self.project_id), folder_path),
                'backup_method': "incremental",
                'compression_status': True,
                'encryption_status': True,
                'retention_policy': "30_days",
                'rpo_compliance': False,
                'rto_estimate': 30,
                'backup_dependencies': json.dumps([])
            }
            data.append(file_entry)
        
        return pd.DataFrame(data)
    
    def update_backup_status(self, file_id: str, success: bool, error_message: str = None, **kwargs) -> None:
        """
        Update backup status in backup_tracker table.
        """


async def update_folders(session: Any, batch_size: int = 1000, max_records: Optional[int] = None, resume_offset: int = 0) -> Tuple[bool, int]:
    """
    Update folders table with data from Filevine API with batch upsert for reliability.
    Implements batch processing with database commits after each batch to prevent data loss.
    
    Args:
        session: Authenticated FilevineSession
        batch_size: Number of records to process in each batch
        max_records: Maximum number of records to process (None for all)
        resume_offset: Optional offset to resume from after a previous failure
        
    Returns:
        Tuple containing (success status, last successfully processed offset)
    """
    # Initialize imports to avoid circular imports
    _init_imports()
    
    logger = get_logger()
    logger.info(f"Starting folder update task with batch size {batch_size}")
    
    # Track statistics for reporting
    total_folders = 0
    current_offset = resume_offset
    last_success_offset = resume_offset
    
    try:
        # If resuming, log that information
        if resume_offset > 0:
            logger.info(f"Resuming folder update from offset {resume_offset}")
        
        # Create a paginator that will handle batch retrieval
        paginator = FolderPaginator(session, offset=str(current_offset), limit=str(batch_size))
        
        # Process folders in batches
        async for folder_batch_df in paginator.async_paginate(paginator.async_fetch, max_records, return_chunks=True):
            if folder_batch_df.empty:
                logger.warning(f"Empty batch at offset {current_offset}, stopping")
                break
                
            batch_size = len(folder_batch_df)
            logger.info(f"Processing batch of {batch_size} folders at offset {current_offset}")
            
            try:
                # Update folders table with this batch
                success = initialize_db_table(folder_batch_df, Folder, ['native_id'])
                
                if success:
                    # Update statistics and last successful offset
                    total_folders += batch_size
                    last_success_offset = current_offset
                    logger.info(f"Successfully upserted {batch_size} folders at offset {current_offset}")
                else:
                    logger.error(f"Failed to upsert folders batch at offset {current_offset}")
                    # Log the failure for tracking
                    log_entry = LogEntry(
                        timestamp=datetime.utcnow(),
                        level="ERROR",
                        message=f"Failed to upsert folders batch",
                        context={
                            "offset": current_offset,
                            "batch_size": batch_size,
                            "task": "update_folders"
                        }
                    )
                    db.session.add(log_entry)
                    db.session.commit()
            except Exception as batch_error:
                logger.error(f"Error processing folders batch at offset {current_offset}: {batch_error}")
                # Log the exception
                log_entry = LogEntry(
                    timestamp=datetime.utcnow(),
                    level="ERROR",
                    message=f"Exception processing folders batch: {str(batch_error)}",
                    context={
                        "offset": current_offset,
                        "batch_size": batch_size,
                        "task": "update_folders",
                        "exception": str(batch_error)
                    }
                )
                db.session.add(log_entry)
                db.session.commit()
            
            # Clear the DataFrame to free memory before processing next batch
            del folder_batch_df
            
            # Update offset for next iteration
            current_offset += batch_size
        
        # Final report
        logger.info(f"Folder update completed: processed {total_folders} folders, last successful offset: {last_success_offset}")
        
        # Return success status and the last successful offset for possible resume
        return total_folders > 0, last_success_offset
    
    except Exception as e:
        logger.error(f"Error updating folders: {e}")
        # Log the failure for tracking/querying on next run
        log_entry = LogEntry(
            timestamp=datetime.utcnow(),
            level="ERROR",
            message=f"Failed to complete folder update task: {str(e)}",
            context={
                "last_success_offset": last_success_offset,
                "total_processed": total_folders,
                "task": "update_folders",
                "exception": str(e)
            }
        )
        try:
            db.session.add(log_entry)
            db.session.commit()
        except Exception as log_error:
            logger.error(f"Could not log folder update failure: {log_error}")
        
        return False, last_success_offset


async def update_documents(session: Any, project_ids: Optional[List[int]] = None, batch_size: int = 1000, max_records: Optional[int] = None, resume_data: Optional[Dict[str, int]] = None) -> Tuple[bool, Dict[str, int]]:
    """
    Update documents table with data from Filevine API with batch upsert for reliability.
    Can update documents for specific projects or all projects.
    Implements batch processing with database commits after each batch to prevent data loss.
    
    Args:
        session: Authenticated FilevineSession
        project_ids: List of project IDs to fetch documents for (None for all projects)
        batch_size: Number of records to process in each batch
        max_records: Maximum number of records to process per project (None for all)
        resume_data: Dictionary mapping project_id to last successful offset for resuming
        
    Returns:
        Tuple containing (success status, dictionary of project_id -> last successful offset)
    """
    # Initialize imports to avoid circular imports
    _init_imports()
    
    logger = get_logger()
    logger.info(f"Starting document update task with batch size {batch_size}")
    
    try:
        if project_ids is None:
            # Get all project IDs if not specified
            # Use db_session instead of Project.query which may not be available in standalone scripts
            from app import db_session
            projects = db_session.query(Project).with_entities(Project.native_id).all()
            project_ids = [p[0] for p in projects]
            logger.info(f"Found {len(project_ids)} projects to process")
        
        # Initialize resume data if not provided
        if resume_data is None:
            resume_data = {}
            
        # Track statistics for reporting
        total_documents = 0
        successful_projects = 0
        last_success_offsets = {}
        
        for project_id in project_ids:
            # Initialize tracking for this project
            current_offset = resume_data.get(str(project_id), 0)
            project_total_docs = 0
            last_success_offset = current_offset
            
            try:
                logger.info(f"Processing documents for project {project_id} starting at offset {current_offset}")
                
                # Create a paginator for this project that will handle batch retrieval
                paginator = FilePaginator(session, offset=str(current_offset), limit=str(batch_size), projectID=str(project_id))
                
                # Process documents in batches for this project
                async for doc_batch_df in paginator.async_paginate(paginator.async_fetch, max_records, return_chunks=True):
                    if doc_batch_df.empty:
                        logger.warning(f"Empty document batch for project {project_id} at offset {current_offset}, stopping")
                        break
                    
                    batch_size_actual = len(doc_batch_df)
                    logger.info(f"Processing batch of {batch_size_actual} documents for project {project_id} at offset {current_offset}")
                    
                    try:
                        # No need to set project_id as foreign key since we now use native_id/partner_id pattern
                        # Also, the folder_id_native extraction is now handled in results_to_df
                        
                        # Verify that folder_id_native is properly set
                        if 'folder_id_native' not in doc_batch_df.columns or doc_batch_df['folder_id_native'].isna().all():
                            logger.warning("No folder_id_native found in document data after processing, DB constraint may fail")
                        
                        # Update documents table with this batch
                        success = initialize_db_table(doc_batch_df, Document, ['native_id'])
                        
                        if success:
                            # Update statistics and last successful offset
                            project_total_docs += batch_size_actual
                            last_success_offset = current_offset
                            logger.info(f"Successfully upserted {batch_size_actual} documents for project {project_id} at offset {current_offset}")
                        else:
                            logger.error(f"Failed to upsert documents batch for project {project_id} at offset {current_offset}")
                            # Log the failure for tracking
                            log_entry = LogEntry(
                                timestamp=datetime.utcnow(),
                                level="ERROR",
                                message=f"Failed to upsert documents batch",
                                context={
                                    "project_id": project_id,
                                    "offset": current_offset,
                                    "batch_size": batch_size_actual,
                                    "task": "update_documents"
                                }
                            )
                            db.session.add(log_entry)
                            db.session.commit()
                    except Exception as batch_error:
                        logger.error(f"Error processing documents batch for project {project_id} at offset {current_offset}: {batch_error}")
                        # Log the exception
                        log_entry = LogEntry(
                            timestamp=datetime.utcnow(),
                            level="ERROR",
                            message=f"Exception processing documents batch: {str(batch_error)}",
                            context={
                                "project_id": project_id,
                                "offset": current_offset,
                                "batch_size": batch_size_actual,
                                "task": "update_documents",
                                "exception": str(batch_error)
                            }
                        )
                        db.session.add(log_entry)
                        db.session.commit()
                    
                    # Clear the DataFrame to free memory before processing next batch
                    del doc_batch_df
                    
                    # Update offset for next iteration
                    current_offset += batch_size_actual
                
                # Update project statistics
                if project_total_docs > 0:
                    successful_projects += 1
                    total_documents += project_total_docs
                    last_success_offsets[str(project_id)] = last_success_offset
                    logger.info(f"Completed documents update for project {project_id}: processed {project_total_docs} documents")
            
            except Exception as project_error:
                logger.error(f"Error processing project {project_id}: {project_error}")
                # Log the exception for this project
                log_entry = LogEntry(
                    timestamp=datetime.utcnow(),
                    level="ERROR",
                    message=f"Exception processing documents for project: {str(project_error)}",
                    context={
                        "project_id": project_id,
                        "last_success_offset": last_success_offset,
                        "task": "update_documents",
                        "exception": str(project_error)
                    }
                )
                db.session.add(log_entry)
                db.session.commit()
                
                # Still track successful batches for this project
                if last_success_offset > 0:
                    last_success_offsets[str(project_id)] = last_success_offset
        
        # Final report
        logger.info(f"Document update completed: processed {total_documents} documents across {successful_projects} projects")
        
        # Return success status and the last successful offsets for each project
        return successful_projects > 0, last_success_offsets
    
    except Exception as e:
        logger.error(f"Error updating documents: {e}")
        # Log the failure for tracking/querying on next run
        log_entry = LogEntry(
            timestamp=datetime.utcnow(),
            level="ERROR",
            message=f"Failed to complete document update task: {str(e)}",
            context={
                "task": "update_documents",
                "exception": str(e)
            }
        )
        try:
            db.session.add(log_entry)
            db.session.commit()
        except Exception as log_error:
            logger.error(f"Could not log document update failure: {log_error}")
            
        return False, resume_data or {}


def update_backup_status(self, file_id: str, success: bool, error_message: str = None, **kwargs) -> None:
    """
    Update backup status in backup_tracker table.
    """
    try:
        file = Document.query.get(file_id)
        if not file:
            logger = get_logger()
            logger.error(f"File {file_id} not found.")
            return

            tracker = BackupTracker(
                id=str(uuid4()),
                document_id=file_id,
                project_id=self.project_id,
                original_filename=kwargs.get('original_filename', file.filename),
                backup_date=datetime.utcnow(),
                file_size=kwargs.get('file_size', 0),
                file_checksum=kwargs.get('file_checksum', ''),
                file_path=kwargs.get('file_path', ''),
                storage_location=kwargs.get('storage_location', ''),
                backup_type=kwargs.get('backup_type', 'local'),
                content_type=kwargs.get('content_type', ''),
                checksum_verified=kwargs.get('checksum_verified', False),
                verification_date=kwargs.get('verification_date'),
                backup_status='completed' if success else 'failed',
                error_message=error_message or '',
                meta_data=kwargs.get('meta_data', {})  # Using renamed column
            )
            file.backup_complete = 1 if success else 0
            db.session.add(tracker)
            db.session.commit()
    except Exception as e:
        db.session.rollback()
        logger = get_logger()
        logger.error(f"Backup status update error: {e}")