import logging
import os
import inspect

LOG_DIR = os.path.join(os.path.dirname(__file__), 'logs')
LOG_FILE = os.path.join(LOG_DIR, 'app.log')
os.makedirs(LOG_DIR, exist_ok=True)

_formatter = logging.Formatter('[%(asctime)s] %(levelname)s %(name)s: %(message)s')
_handler = logging.FileHandler(LOG_FILE)
_handler.setFormatter(_formatter)
_handler.setLevel(logging.INFO)

logging.basicConfig(level=logging.INFO, handlers=[_handler], force=True)

def get_logger():
    frame = inspect.currentframe().f_back
    module = inspect.getmodule(frame)
    func = frame.f_code.co_name
    name = f"{os.path.splitext(os.path.basename(module.__file__))[0]}.{func}" if module else func
    return logging.getLogger(name)
