import requests
import aiohttp
import asyncio
import pandas as pd
import logging
from utils import get_logger
from datetime import datetime
from typing import Dict, List, Optional, Union, Tuple, Generator, Any, AsyncGenerator
import json
import os
from tenacity import retry, stop_after_attempt, wait_exponential  # For retries; if not available, replace with manual try/except loops.
from config import Config  # Import the Config class for settings
from mysql_db.FV_db import results_to_df  # Import the results_to_df function

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


# Load settings from Config class
clientId = Config.FILEVINE_CLIENT_ID
clientSecret = Config.FILEVINE_CLIENT_SECRET
personalAccessToken = Config.FILEVINE_ACCESS_TOKEN

logger = get_logger()
logger.info("Config settings loaded.")

class FilevineSession:
    """
    Class to create a session object for Filevine authentication.
    
    Attributes:
        accessToken: The authentication token for API access
        refreshToken: Token used for refreshing the access token
        userId: The Filevine user ID
        orgId: The Filevine organization ID
        getHeader: HTTP headers for API requests
    """
    def __init__(self) -> None:
        self.accessToken: str = ""
        self.refreshToken: str = ""
        self.userId: str = ""
        self.orgId: str = ""
        self.getHeader: Dict[str, str] = {}

        self.authenticate()

    def authenticate(self) -> None:
        """
        Authenticate with Filevine API using personal access token.
        Sets up the access token and request headers.
        """
        try:
            token_url = Config.FILEVINE_AUTH_URL or "https://identity.filevine.com/connect/token"
            logger = get_logger()
            logger.debug(f"Authenticating with URL: {token_url}")
            
            data = {
                'grant_type': 'personal_access_token',
                'scope': 'fv.api.gateway.access tenant filevine.v2.api.* openid email fv.auth.tenant.read',
                'client_id': Config.FILEVINE_CLIENT_ID,
                'client_secret': Config.FILEVINE_CLIENT_SECRET,
                'token': Config.FILEVINE_ACCESS_TOKEN
            }
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
            
            response = requests.post(token_url, data=data, headers=headers)
            logger = get_logger()
            logger.debug(f"Auth response status: {response.status_code}")
            
            if response.status_code != 200:
                raise Exception(f"Auth failed: {response.status_code}: {response.text}")
                
            response_data = response.json()
            self.accessToken = response_data['access_token']
            logger = get_logger()
            logger.info("Access Token received.")
            self.userId = Config.FILEVINE_USER_ID
            self.orgId = Config.FILEVINE_ORG_ID
            
            self.getHeader = {
                'x-fv-orgid': self.orgId,
                'x-fv-userid': self.userId,
                'Accept': 'application/json',
                'Authorization': f'Bearer {self.accessToken}'
            }
        except Exception as e:
            logger = get_logger()
            logger.error(f"Authentication error: {e}")
            raise

def make_request(session: FilevineSession, url: str, params: Optional[Dict[str, str]] = None) -> Tuple[Union[requests.Response, bool], FilevineSession]:
    """
    Synchronous request helper with auth refresh.
    """
    response = requests.get(url, headers=session.getHeader, params=params)
    if response.status_code == 401:
        session.authenticate()
        response = requests.get(url, headers=session.getHeader, params=params)
    if response.status_code == 404:
        with open('request_errors.txt', 'a') as log_file:
            log_file.write(f"{datetime.now()} - 404 Not Found: {url}\n")
        return False, session
    return response, session

async def async_make_request(session: FilevineSession, url: str, params: Optional[Dict[str, str]] = None) -> Tuple[Union[aiohttp.ClientResponse, bool], FilevineSession]:
    """
    Asynchronous request helper with robust retry mechanism for intermittent failures.
    Handles 401 (auth), 404, 429 (rate limit), 500-level errors, and network timeouts.
    """
    logger = get_logger()
    logger.debug(f"Making async request to: {url} with params: {params}")
    
    # Configure retry parameters
    max_retries = 5
    retry_statuses = [404, 429, 500, 502, 503, 504]
    retry_delay_base = 2  # exponential backoff base (seconds)
    
    retries = 0
    last_exception = None
    
    while retries <= max_retries:
        try:
            # Add jitter to prevent thundering herd
            if retries > 0:
                jitter = random.uniform(0, 1)
                delay = (retry_delay_base ** retries) + jitter
                logger.info(f"Retry {retries}/{max_retries} for {url} after {delay:.2f}s")
                await asyncio.sleep(delay)
            
            async with aiohttp.ClientSession(headers=session.getHeader) as async_session:
                async with async_session.get(url, params=params, timeout=30) as response:
                    # Handle authentication failure
                    if response.status == 401:
                        logger.debug("Authentication failed, refreshing token...")
                        session.authenticate()
                        continue  # Retry immediately with new token
                    
                    logger.debug(f"Response status: {response.status} for {url}")
                    
                    # Handle retryable status codes
                    if response.status in retry_statuses:
                        if retries < max_retries:
                            retries += 1
                            error_msg = f"{response.status} error accessing {url} (Retry {retries}/{max_retries})"
                            logger.warning(error_msg)
                            with open('request_errors.txt', 'a') as log_file:
                                log_file.write(f"{datetime.now()} - {error_msg}\n")
                            continue
                        else:
                            # Max retries reached
                            logger.error(f"Max retries ({max_retries}) reached for {url} with status {response.status}")
                            return False, session
                    
                    # Handle successful response
                    if response.status == 200:
                        content = await response.read()
                        logger.debug(f"Response content length: {len(content)}")
                        response._body = content
                        return response, session
                    
                    # Other non-retryable error status
                    logger.error(f"Non-retryable status {response.status} for {url}")
                    return False, session
                    
        except aiohttp.ClientError as e:
            last_exception = e
            logger.warning(f"Client error in async request: {e} (Retry {retries}/{max_retries})")
        except asyncio.TimeoutError as e:
            last_exception = e
            logger.warning(f"Timeout error connecting to {url} (Retry {retries}/{max_retries})")
        except Exception as e:
            last_exception = e
            logger.error(f"Unexpected error in async_make_request: {e}")
        
        retries += 1
        
    # If we get here, all retries failed
    logger.error(f"All {max_retries} retries failed for {url}. Last error: {last_exception}")
    return False, session

class Paginator:
    """
    Base class for paginating through API results.
    """
    def __init__(self, session: FilevineSession, offset: str = '0', limit: str = '1000') -> None:
        self.session: FilevineSession = session
        self.offset: str = offset
        self.limit: str = limit

    def paginate(
        self, 
        fetch_func: callable, 
        max_records: Optional[int] = None, 
        return_chunks: bool = False
    ) -> Union[pd.DataFrame, Generator[pd.DataFrame, None, None]]:
        """
        Synchronous pagination.
        """
        all_data = []
        offset = int(self.offset)
        limit = int(self.limit)
        total_records = 0

        while True:
            response, self.session = fetch_func(self.session, str(offset), str(limit))
            if not response:
                break

            try:
                data = response.json()
            except Exception as e:
                logger.error(f"JSON decode error: {e}")
                break

            if 'items' not in data:
                break

            df = results_to_df(response)  # Assuming imported from FV_db.py
            if df is None or df.empty:
                break

            records_fetched = len(df)
            total_records += records_fetched

            if return_chunks:
                yield df
            else:
                all_data.append(df)

            if max_records and total_records >= max_records:
                break

            if not data.get('hasMore', False):
                break

            offset += limit

        if not return_chunks:
            return pd.concat(all_data) if all_data else pd.DataFrame()

    async def async_paginate(
        self, 
        fetch_func: callable, 
        max_records: Optional[int] = None, 
        return_chunks: bool = False
    ) -> AsyncGenerator[pd.DataFrame, None]:
        """
        Asynchronous pagination for better performance.
        """
        all_data = []
        offset = int(self.offset)
        limit = int(self.limit)
        total_records = 0

        while True:
            response, self.session = await fetch_func(self.session, str(offset), str(limit))
            if not response:
                break

            try:
                data = await response.json()
            except Exception as e:
                logger.error(f"JSON decode error: {e}")
                break

            if 'items' not in data:
                logger.warning(f"No 'items' found in response data. Keys: {data.keys() if isinstance(data, dict) else 'N/A'}")
                break

            # Determine correct table type based on the paginator class
            table_type = None
            if isinstance(self, ProjectPaginator):
                table_type = 'projects'
            elif isinstance(self, FolderPaginator):
                table_type = 'folders'
            elif isinstance(self, FilePaginator):
                table_type = 'documents'
                
            # Force the correct table type to prevent misidentification
            df = await results_to_df(data, force_table_type=table_type)
            if df is None or df.empty:
                break

            records_fetched = len(df)
            total_records += records_fetched

            if return_chunks:
                yield df
            else:
                all_data.append(df)

            if max_records and total_records >= max_records:
                break

            if not data.get('hasMore', False):
                break

            offset += limit

        if not return_chunks:
            yield pd.concat(all_data) if all_data else pd.DataFrame()

class ProjectPaginator(Paginator):
    """
    Paginator for fetching and processing project data.
    """
    def fetch(self, session: FilevineSession, offset: str, limit: str) -> Tuple[requests.Response, FilevineSession]:
        """
        Synchronous fetch for projects.
        """
        URL = f"{Config.FILEVINE_API_URL}/fv-app/v2/projects"
        params = {'offset': offset, 'limit': limit}
        return make_request(session, URL, params)

    async def async_fetch(self, session: FilevineSession, offset: str, limit: str) -> Tuple[aiohttp.ClientResponse, FilevineSession]:
        """
        Asynchronous fetch for projects.
        """
        URL = f"{Config.FILEVINE_API_URL}/fv-app/v2/projects"
        params = {'offset': offset, 'limit': limit}
        return await async_make_request(session, URL, params)

    def process(self, df: pd.DataFrame) -> None:
        """
        Process project data; externalize DB insert.
        """
        # populate_projects_table(df)  # Comment out; handle externally if needed.
        logger = get_logger()
        logger.info(f"Processed {len(df)} projects.")

class FolderPaginator(Paginator):
    """
    Paginator for fetching and processing folder data.
    """
    def __init__(self, session: FilevineSession, offset: str = '0', limit: str = '1000', projectId: Optional[str] = None) -> None:
        super().__init__(session, offset, limit)
        self.projectId: Optional[str] = projectId

    def fetch(self, session: FilevineSession, offset: str, limit: str) -> Tuple[requests.Response, FilevineSession]:
        """
        Synchronous fetch for folders.
        """
        URL = f"{Config.FILEVINE_API_URL}/fv-app/v2/Folders/list"
        params = {'offset': offset, 'limit': limit, 'includeArchivedProjects': 'true', 'includeArchivedFolders': 'true'}
        if self.projectId:
            params['projectId'] = self.projectId
        return make_request(session, URL, params)

    async def async_fetch(self, session: FilevineSession, offset: str, limit: str) -> Tuple[aiohttp.ClientResponse, FilevineSession]:
        """
        Asynchronous fetch for folders.
        """
        URL = f"{Config.FILEVINE_API_URL}/fv-app/v2/Folders/list"
        params = {'offset': offset, 'limit': limit, 'includeArchivedProjects': 'true', 'includeArchivedFolders': 'true'}
        if self.projectId:
            params['projectId'] = self.projectId
        return await async_make_request(session, URL, params)

    def process(self, df: pd.DataFrame) -> None:
        """
        Process folder data; externalize DB insert.
        """
        # populate_folders_table(df, default_project_id=self.projectId)  # External.
        logger = get_logger()
        logger.info(f"Processed {len(df)} folders.")

class FilePaginator(Paginator):
    """
    Paginator for fetching and processing file/document data.
    """
    def __init__(self, session: FilevineSession, offset: str = '0', limit: str = '1000', projectID: Optional[str] = None) -> None:
        super().__init__(session, offset, limit)
        self.projectID: Optional[str] = projectID

    def fetch(self, session: FilevineSession, offset: str, limit: str) -> Tuple[requests.Response, FilevineSession]:
        """
        Synchronous fetch for files/documents.
        """
        if not self.projectID:
            raise ValueError("projectID required for document fetch.")
        URL = f"{Config.FILEVINE_API_URL}/fv-app/v2/projects/{self.projectID}/documents"
        params = {'offset': offset, 'limit': limit, 'includeArchived': 'true'}
        return make_request(session, URL, params)

    async def async_fetch(self, session: FilevineSession, offset: str, limit: str) -> Tuple[aiohttp.ClientResponse, FilevineSession]:
        """
        Asynchronous fetch for files/documents.
        """
        if not self.projectID:
            raise ValueError("projectID required for document fetch.")
        URL = f"{Config.FILEVINE_API_URL}/fv-app/v2/projects/{self.projectID}/documents"
        params = {'offset': offset, 'limit': limit, 'includeArchived': 'true'}
        return await async_make_request(session, URL, params)

    def process(self, df: pd.DataFrame) -> None:
        """
        Process document data; externalize DB insert.
        """
        # Document table logic handled externally in FV_db.py
        logger = get_logger()
        logger.info(f"Processed {len(df)} documents for project {self.projectID}.")

async def getAllProjects(session: FilevineSession, offset: str = '0', limit: str = '1000', max_records: Optional[int] = None) -> pd.DataFrame:
    """
    Fetches all projects asynchronously.
    """
    paginator = ProjectPaginator(session, offset, limit)
    all_df = pd.DataFrame()
    async for chunk_df in paginator.async_paginate(paginator.async_fetch, max_records, return_chunks=True):
        all_df = pd.concat([all_df, chunk_df])
        paginator.process(chunk_df)
    return all_df

async def getAllFolders(session: FilevineSession, offset: str = '0', limit: str = '1000', max_records: Optional[int] = None) -> pd.DataFrame:
    """
    Fetches all folders asynchronously.
    """
    paginator = FolderPaginator(session, offset, limit)
    all_df = pd.DataFrame()
    async for chunk_df in paginator.async_paginate(paginator.async_fetch, max_records, return_chunks=True):
        all_df = pd.concat([all_df, chunk_df])
        paginator.process(chunk_df)
    return all_df

async def getAllDocuments(session: FilevineSession, projectID: str, offset: str = '0', limit: str = '1000', max_records: Optional[int] = None) -> pd.DataFrame:
    """
    Fetches all documents for a specific project asynchronously.
    
    Args:
        session: Authenticated FilevineSession
        projectID: Project ID to fetch documents for
        offset: Starting offset for pagination
        limit: Number of records per page
        max_records: Maximum total records to fetch (None for all)
        
    Returns:
        DataFrame containing document data
    """
    paginator = FilePaginator(session, offset, limit, projectID)
    all_df = pd.DataFrame()
    async for chunk_df in paginator.async_paginate(paginator.async_fetch, max_records, return_chunks=True):
        all_df = pd.concat([all_df, chunk_df])
        paginator.process(chunk_df)
    return all_df

async def getProjectFiles(session: FilevineSession, projectID: str, offset: str = '0', limit: str = '1000', max_records: Optional[int] = None) -> pd.DataFrame:
    """
    Fetches files for a project asynchronously.
    """
    paginator = FilePaginator(session, offset, limit, projectID)
    all_df = pd.DataFrame()
    async for chunk_df in paginator.async_paginate(paginator.async_fetch, max_records, return_chunks=True):
        all_df = pd.concat([all_df, chunk_df])
        paginator.process(chunk_df)
    return all_df

def make_post_request(session: FilevineSession, url: str, json_data: Dict[str, Any]) -> Tuple[requests.Response, FilevineSession]:
    """
    Helper for POST requests.
    """
    headers = session.getHeader.copy()
    headers['Content-Type'] = 'application/json'
    response = requests.post(url, headers=headers, json=json_data)
    if response.status_code == 401:
        session.authenticate()
        response = requests.post(url, headers=headers, json=json_data)
    if response.status_code == 404:
        with open('request_errors.txt', 'a') as log_file:
            log_file.write(f"{datetime.now()} - 404 Not Found: {url}\n")
        response = requests.Response()
        response.status_code = 404
    return response, session

def batch_download_urls(session: FilevineSession, document_ids: List[str]) -> Tuple[Dict[str, Any], int]:
    """
    Request batch download URLs for documents.
    Returns JSON with metadata including AWS download links.
    """
    if not document_ids:
        logger = get_logger()
        logger.warning("Empty document_ids; returning empty.")
        return {}, 200
    URL = "https://api.filevineapp.com/fv-app/v2/Documents/batch/download"
    payload = {"DocumentIds": document_ids, "DownloadUrlTimeToLive": '10800'}  # 3 hours
    logger = get_logger()
    logger.debug(f"POST request to {URL} with payload: {payload}")
    response, session = make_post_request(session, URL, payload)
    try:
        return response.json(), response.status_code
    except requests.exceptions.JSONDecodeError:
        logger = get_logger()
        logger.error(f"JSON decode failed. Status: {response.status_code}")
        return {}, response.status_code

# Example main for testing
async def main():
    session = FilevineSession()
    projects = await getAllProjects(session)
    logger = get_logger()
    logger.info(f"Fetched {len(projects)} projects.")
    # Example: Get files for a project, then batch URLs.
    if not projects.empty:
        sample_project_id = projects.iloc[0].get('projectId')  # Assume column.
        files_df = await getProjectFiles(session, sample_project_id)
        document_ids = files_df['id'].tolist()  # Assume 'id' column.
        download_data, status = batch_download_urls(session, document_ids)
        logger = get_logger()
        logger.info(f"Batch download response status: {status}")
        # Caller would iterate download_data for AWS links and download.

if __name__ == "__main__":
    asyncio.run(main())