"""
Project-specific operations for Filevine data management.

This module contains functions and classes for managing project data
and project-related operations.
"""

import pandas as pd
from typing import List, Optional, Any, Tuple
from utils import get_logger
from mysql_db.models import Project
from mysql_db.table_operations import initialize_db_table
from app import db


class ProjectManager:
    """Manager class for project-related operations."""
    
    def __init__(self):
        self.logger = get_logger()
    
    def get_all_project_ids(self) -> List[int]:
        """
        Get all project IDs from the database.
        
        Returns:
            List of project native IDs
        """
        try:
            projects = db.session.query(Project).with_entities(Project.native_id).all()
            return [p[0] for p in projects]
        except Exception as e:
            self.logger.error(f"Error fetching project IDs: {e}")
            return []
    
    def get_project_by_id(self, project_id: int) -> Optional[Project]:
        """
        Get a project by its native ID.
        
        Args:
            project_id: The native ID of the project
            
        Returns:
            Project object or None if not found
        """
        try:
            return db.session.query(Project).filter_by(native_id=project_id).first()
        except Exception as e:
            self.logger.error(f"Error fetching project {project_id}: {e}")
            return None
    
    def update_projects_table(self, df: pd.DataFrame) -> bool:
        """
        Update the projects table with new data.
        
        Args:
            df: DataFrame containing project data
            
        Returns:
            Success status
        """
        if df.empty:
            self.logger.warning("Empty DataFrame provided for projects update")
            return False
        
        try:
            success = initialize_db_table(df, Project, ['native_id'])
            if success:
                self.logger.info(f"Successfully updated {len(df)} projects")
            else:
                self.logger.error("Failed to update projects table")
            return success
        except Exception as e:
            self.logger.error(f"Error updating projects table: {e}")
            return False
    
    def get_projects_summary(self) -> dict:
        """
        Get a summary of projects in the database.
        
        Returns:
            Dictionary with project statistics
        """
        try:
            total_projects = db.session.query(Project).count()
            archived_projects = db.session.query(Project).filter_by(is_archived=True).count()
            active_projects = total_projects - archived_projects
            
            return {
                'total_projects': total_projects,
                'active_projects': active_projects,
                'archived_projects': archived_projects
            }
        except Exception as e:
            self.logger.error(f"Error getting projects summary: {e}")
            return {
                'total_projects': 0,
                'active_projects': 0,
                'archived_projects': 0
            }


async def update_projects(session: Any, batch_size: int = 1000, max_records: Optional[int] = None, resume_offset: int = 0) -> Tuple[bool, int]:
    """
    Update projects table with data from Filevine API.
    
    Args:
        session: Authenticated FilevineSession
        batch_size: Number of records to process in each batch
        max_records: Maximum number of records to process (None for all)
        resume_offset: Optional offset to resume from after a previous failure
        
    Returns:
        Tuple containing (success status, last successfully processed offset)
    """
    # Initialize imports to avoid circular imports
    from mysql_db.FV_db import _init_imports
    _init_imports()
    
    # Import paginator after initialization
    from filevine.FV_API import ProjectPaginator
    
    logger = get_logger()
    logger.info(f"Starting project update task with batch size {batch_size}")
    
    # Track statistics for reporting
    total_projects = 0
    current_offset = resume_offset
    last_success_offset = resume_offset
    
    try:
        # If resuming, log that information
        if resume_offset > 0:
            logger.info(f"Resuming project update from offset {resume_offset}")
        
        # Create a paginator that will handle batch retrieval
        paginator = ProjectPaginator(session, offset=str(current_offset), limit=str(batch_size))
        
        # Process projects in batches
        async for project_batch_df in paginator.async_paginate(paginator.async_fetch, max_records, return_chunks=True):
            if project_batch_df.empty:
                logger.warning(f"Empty batch at offset {current_offset}, stopping")
                break
                
            batch_size_actual = len(project_batch_df)
            logger.info(f"Processing batch of {batch_size_actual} projects at offset {current_offset}")
            
            try:
                # Update projects table with this batch
                success = initialize_db_table(project_batch_df, Project, ['native_id'])
                
                if success:
                    # Update statistics and last successful offset
                    total_projects += batch_size_actual
                    last_success_offset = current_offset
                    logger.info(f"Successfully upserted {batch_size_actual} projects at offset {current_offset}")
                else:
                    logger.error(f"Failed to upsert projects batch at offset {current_offset}")
            except Exception as batch_error:
                logger.error(f"Error processing projects batch at offset {current_offset}: {batch_error}")
            
            # Clear the DataFrame to free memory before processing next batch
            del project_batch_df
            
            # Update offset for next iteration
            current_offset += batch_size_actual
        
        # Final report
        logger.info(f"Project update completed: processed {total_projects} projects, last successful offset: {last_success_offset}")
        
        # Return success status and the last successful offset for possible resume
        return total_projects > 0, last_success_offset
    
    except Exception as e:
        logger.error(f"Error updating projects: {e}")
        return False, last_success_offset
