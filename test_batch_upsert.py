#!/usr/bin/env python
import asyncio
import logging
from config import Config
from filevine.FV_API import FilevineSession
from mysql_db.FV_db import update_folders, update_documents
from utils import get_logger
import time
import sys

# Import necessary SQLAlchemy components
from app import db, init_app, db_session
from mysql_db.models import Project, Folder, Document, LogEntry

# Configure logging to stdout for better visibility
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)
handler = logging.StreamHandler(sys.stdout)
handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
root_logger.addHandler(handler)

"""
Test script to verify the batch upsert logic for folders and documents
with resume functionality and SQLAlchemy relationships.
"""

logger = get_logger()

async def test_folder_update():
    """Test folder update with batch upsert logic"""
    logger.info("=== Testing folder update with batch upsert ===")
    
    # Initialize session
    logger.info("Initializing FilevineSession...")
    session = FilevineSession()
    
    # Test resume functionality by starting with a non-zero offset
    resume_offset = 0  # Change to test resume functionality
    logger.info(f"Starting folder update with resume_offset={resume_offset}")
    
    # Update folders with batch size of 50
    start_time = time.time()
    success, last_offset = await update_folders(session, batch_size=50, max_records=100, resume_offset=resume_offset)
    end_time = time.time()
    
    logger.info(f"Folder update completed in {end_time - start_time:.2f} seconds")
    logger.info(f"Success: {success}, Last successful offset: {last_offset}")

async def test_document_update():
    """Test document update with batch upsert logic"""
    logger.info("=== Testing document update with batch upsert ===")
    
    # Initialize session
    logger.info("Initializing FilevineSession...")
    session = FilevineSession()
    
    # Test with specific project IDs to limit scope
    # Get these from your projects table
    test_project_ids = None  # Replace with actual project IDs or keep None to process all
    
    # Test resume functionality with empty resume_data dict
    resume_data = {}
    logger.info(f"Starting document update for projects: {test_project_ids or 'ALL'}")
    
    # Update documents with batch size of 50
    start_time = time.time()
    success, last_offsets = await update_documents(
        session, 
        project_ids=test_project_ids, 
        batch_size=50, 
        max_records=100,  # Limit records for testing
        resume_data=resume_data
    )
    end_time = time.time()
    
    logger.info(f"Document update completed in {end_time - start_time:.2f} seconds")
    logger.info(f"Success: {success}, Last successful offsets: {last_offsets}")

async def main():
    """Run all tests sequentially"""
    try:
        logger.info("Starting batch upsert tests")
        
        # Initialize the database and create tables if needed
        logger.info("Initializing database connection...")
        init_app()
        logger.info("Database initialized successfully")
        
        # Verify database connection by querying the number of projects
        project_count = db_session.query(Project).count()
        logger.info(f"Found {project_count} projects in the database")
        
        # Test folder update
        await test_folder_update()
        
        # Test document update
        await test_document_update()
        
        logger.info("All tests completed successfully")
    except Exception as e:
        logger.error(f"Test failed: {e}")
    finally:
        # Ensure the session is cleaned up
        db_session.remove()

if __name__ == "__main__":
    asyncio.run(main())
