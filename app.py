from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from sqlalchemy.ext.declarative import declarative_base
from config import Config

# Construct database URI from configuration
def get_database_uri():
    # Explicitly use pymysql as the MySQL driver
    user = Config.DB_USER
    password = Config.DB_PASSWORD
    host = Config.DB_HOST
    port = Config.DB_PORT
    database = Config.DB_NAME
    
    if not all([user, password, host, database]):
        # Fallback to SQLite if proper MySQL config is not available
        return 'sqlite:///bls_backups.db'
        
    # Force use of pymysql regardless of what DB_ENGINE might be set to
    return f'mysql+pymysql://{user}:{password}@{host}:{port}/{database}'

# Create SQLAlchemy engine and session factory
engine = create_engine(get_database_uri())
db_session = scoped_session(
    sessionmaker(autocommit=False, autoflush=False, bind=engine)
)

# Define a base class for declarative models
Base = declarative_base()
Base.query = db_session.query_property()

class SQLAlchemy:
    def __init__(self):
        self.session = db_session
        self.Model = Base
        
    def create_all(self):
        Base.metadata.create_all(bind=engine)
        
    def drop_all(self):
        Base.metadata.drop_all(bind=engine)

# Create a db instance that will be imported by other modules
db = SQLAlchemy()

def init_app():
    """Initialize the database"""
    db.create_all()
    return db

# Initialize tables if this module is run directly
if __name__ == "__main__":
    init_app()
