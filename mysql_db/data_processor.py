"""
Data processing utilities for converting Filevine API responses to DataFrames.

This module handles the conversion of API response data to pandas DataFrames
with proper field mapping and data transformation for database insertion.
"""

import json
import pandas as pd
from typing import Dict, List, Optional, Union, Any
from utils import get_logger
from mysql_db.mappings import extract_native_id, get_table_mappings, CommonMappings


async def results_to_df(response_or_data: Any, force_table_type: Optional[str] = None) -> pd.DataFrame:
    """
    Convert API response or parsed data to DataFrame for database insertion.
    
    Args:
        response_or_data: Response object from requests/aiohttp OR pre-parsed data dictionary
        force_table_type: Force a specific table type instead of auto-detection
        
    Returns:
        Processed pandas DataFrame
    """
    logger = get_logger()
    if not response_or_data:
        logger.warning("Empty response or data received in results_to_df")
        return pd.DataFrame()
    
    # Parse response data
    data = await _parse_response_data(response_or_data, logger)
    if data is None:
        return pd.DataFrame()
    
    # Validate response format
    if not isinstance(data, dict):
        logger.error(f"Unexpected response format, expected dict got {type(data)}")
        return pd.DataFrame()
        
    if 'items' not in data:
        logger.warning(f"No 'items' found in response data. Keys: {data.keys() if isinstance(data, dict) else 'N/A'}")
        return pd.DataFrame()
        
    df = pd.DataFrame(data['items'])
    
    # Determine table type
    table_type = force_table_type or _detect_table_type(df, logger)
    
    if table_type not in ['projects', 'folders', 'documents']:
        logger.error(f"Invalid table type: {table_type}. Unable to map fields properly.")
        return pd.DataFrame()
    
    logger.info(f"Processing data for table type: {table_type} {'(forced)' if force_table_type else '(auto-detected)'}")
    
    # Process the DataFrame based on table type
    df = _process_dataframe_by_type(df, table_type, data, logger)
    
    return df


async def _parse_response_data(response_or_data: Any, logger) -> Optional[Dict]:
    """Parse response data from various response object types."""
    # Determine if we're working with a response object or a pre-parsed data dictionary
    if isinstance(response_or_data, dict):
        # Already a parsed data dictionary
        logger.debug("Using pre-parsed data dictionary")
        return response_or_data
    
    # Process as a response object
    try:
        # Handle different response types
        if hasattr(response_or_data, 'json') and callable(response_or_data.json):
            # requests Response object
            data = response_or_data.json()
            logger.debug("Parsed JSON from requests Response object")
            return data
        elif hasattr(response_or_data, '_body') and response_or_data._body:
            # aiohttp ClientResponse with cached body
            try:
                data = json.loads(response_or_data._body)
                logger.debug(f"Parsed response body from cached content: {len(response_or_data._body)} bytes")
                return data
            except json.JSONDecodeError as e:
                logger.error(f"Failed to parse cached response body: {e}")
                return None
        else:
            # Standard aiohttp ClientResponse
            try:
                data = await response_or_data.json()
                logger.debug("Parsed JSON from aiohttp ClientResponse")
                return data
            except Exception as e:
                logger.error(f"Failed to decode JSON from response: {e}")
                # Try reading text as a fallback
                try:
                    text = await response_or_data.text()
                    logger.debug(f"Response text content length: {len(text)}")
                    return json.loads(text)
                except Exception as text_e:
                    logger.error(f"Failed to read response text: {text_e}")
                    return None
    except Exception as e:
        logger.error(f"JSON decode error: {e}")
        return None


def _detect_table_type(df: pd.DataFrame, logger) -> Optional[str]:
    """Auto-detect table type based on DataFrame columns."""
    if 'projectId' in df.columns:
        return 'projects'
    elif 'folderId' in df.columns:
        return 'folders'
    elif 'documentId' in df.columns:
        return 'documents'
    
    logger.warning(f"Could not auto-detect table type from columns: {df.columns.tolist()}")
    return None


def _process_dataframe_by_type(df: pd.DataFrame, table_type: str, raw_data: Dict, logger) -> pd.DataFrame:
    """Process DataFrame based on table type with appropriate field mappings."""
    # Get mappings for this table type
    mappings = get_table_mappings(table_type)
    
    # Process primary ID field
    df = _process_primary_id(df, mappings, logger)
    
    # Process foreign key relationships
    if 'foreign_key_mappings' in mappings:
        df = _process_foreign_keys(df, mappings['foreign_key_mappings'], table_type, raw_data, logger)
    
    # Handle common metadata fields
    df = _process_common_fields(df, logger)
    
    # Apply field mappings
    df = _apply_field_mappings(df, mappings['field_mappings'], logger)
    
    return df


def _process_primary_id(df: pd.DataFrame, mappings: Dict, logger) -> pd.DataFrame:
    """Process the primary ID field for the table."""
    primary_id_field = mappings['primary_id_field']
    
    if primary_id_field in df.columns:
        df['native_id'] = df[primary_id_field].apply(extract_native_id)
        df = df.drop(columns=[primary_id_field])
        logger.debug(f"Processed primary ID field: {primary_id_field}")
    
    return df


def _process_foreign_keys(df: pd.DataFrame, fk_mappings: Dict, table_type: str, raw_data: Dict, logger) -> pd.DataFrame:
    """Process foreign key relationships based on table type."""
    for api_field, db_field in fk_mappings.items():
        if api_field in df.columns:
            df[db_field] = df[api_field].apply(extract_native_id)
            df = df.drop(columns=[api_field])
            logger.info(f"Mapped {api_field} to {db_field} for {len(df)} {table_type}")
        elif table_type == 'documents' and api_field in ['projectId', 'folderId']:
            # Special handling for documents that might have IDs in links
            df = _extract_id_from_links(df, api_field, db_field, logger)

    # Special processing for specific table types
    if table_type == 'folders':
        df = _process_folder_names(df, raw_data, logger)
    elif table_type == 'documents':
        df = _process_document_links(df, logger)
        df = _validate_document_foreign_keys(df, logger)

    return df


def _extract_id_from_links(df: pd.DataFrame, api_field: str, db_field: str, logger) -> pd.DataFrame:
    """Extract foreign key IDs from links field for documents."""
    if 'links' not in df.columns or df.empty:
        return df
    
    try:
        link_field = 'project' if api_field == 'projectId' else 'parent'
        
        if isinstance(df.iloc[0]['links'], dict) and link_field in df.iloc[0]['links']:
            logger.info(f"Extracting {db_field} from links.{link_field} URL")
            df[db_field] = df['links'].apply(
                lambda x: int(x.get(link_field, '').split('/')[-1]) if isinstance(x, dict) and link_field in x else None
            )
            logger.info(f"Extracted {db_field} from links for {len(df[df[db_field].notnull()])} documents")
    except Exception as e:
        logger.error(f"Error extracting {db_field} from links: {e}")
    
    return df


def _process_common_fields(df: pd.DataFrame, logger) -> pd.DataFrame:
    """Handle common metadata fields that all tables might have."""
    common_fields = CommonMappings.get_common_id_fields()
    
    for field in common_fields:
        if field in df.columns:
            target_field = CommonMappings.convert_id_field_name(field)
            df[target_field] = df[field].apply(extract_native_id)
            df = df.drop(columns=[field])
    
    return df


def _apply_field_mappings(df: pd.DataFrame, field_mappings: Dict, logger) -> pd.DataFrame:
    """Apply field mappings to rename columns from API names to database column names."""
    for api_field, db_field in field_mappings.items():
        if api_field in df.columns:
            # Only copy to new column name if API field and DB field are different
            if api_field != db_field:
                df[db_field] = df[api_field]
                df = df.drop(columns=[api_field])

    return df


def _process_folder_names(df: pd.DataFrame, raw_data: Dict, logger) -> pd.DataFrame:
    """Special processing for folder names that might be missing from DataFrame."""
    if not df.empty:
        logger.info(f"First folder raw data sample: {json.dumps(raw_data['items'][0] if isinstance(raw_data, dict) and 'items' in raw_data else raw_data[0], indent=2)[:500]}")
        logger.info(f"Available folder columns: {df.columns.tolist()}")

        # Ensure the name column is present
        if 'name' in df.columns:
            logger.info(f"Found name in folders: {df['name'].head(3).tolist()}")
        else:
            logger.warning("'name' column not found in folder data, will try to create it")
            # If name isn't in columns, look in potential nested objects
            if 'folderId' in df.columns and df.iloc[0]['folderId'] is not None and isinstance(df.iloc[0]['folderId'], dict):
                # Try to extract name from raw data and add it to DataFrame
                try:
                    if isinstance(raw_data, dict) and 'items' in raw_data:
                        # Extract names from raw data
                        names = [item.get('name', '') for item in raw_data['items']]
                        df['name'] = names
                        logger.info(f"Extracted folder names from raw data: {names[:3]}")
                    elif isinstance(raw_data, list):
                        names = [item.get('name', '') for item in raw_data]
                        df['name'] = names
                        logger.info(f"Extracted folder names from raw data list: {names[:3]}")
                except Exception as e:
                    logger.error(f"Error extracting folder names: {e}")

    return df


def _process_document_links(df: pd.DataFrame, logger) -> pd.DataFrame:
    """Process document links field - remove it since we extract IDs directly."""
    if 'links' in df.columns:
        # Just drop the links field as we're getting folder_id_native from folderId.native
        df = df.drop(columns=['links'])
        logger.info("Links field dropped - using folderId.native directly")

    return df


def _validate_document_foreign_keys(df: pd.DataFrame, logger) -> pd.DataFrame:
    """Validate that required foreign keys are present for documents."""
    # Final check for required foreign keys
    if 'project_id_native' not in df.columns or df['project_id_native'].isnull().all():
        logger.warning("No project_id_native found in documents data, foreign key constraint will fail")
    if 'folder_id_native' not in df.columns or df['folder_id_native'].isnull().all():
        logger.warning("No folder_id_native found in documents data, foreign key constraint will fail")

    return df
