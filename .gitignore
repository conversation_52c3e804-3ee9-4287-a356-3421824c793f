# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
env/
bls_venv/

# IDEs and editors
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Logs and databases
*.log
logs/*.log
*.sqlite3
*.db
*.sql
*.dump

# Exception: track the logs folder itself, but not its contents
!logs/.gitkeep

# Configuration and sensitive data
.env
.env.local
credentials.json
*config.json
*secrets*

# Database migrations (may contain sensitive data)
migrations/

# Cache
.cache/
.pytest_cache/
.coverage
htmlcov/

# System Files
.DS_Store
Thumbs.db

# Distribution / packaging
.Python
