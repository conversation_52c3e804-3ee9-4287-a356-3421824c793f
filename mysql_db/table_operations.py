"""
Database table operations for Filevine data management.

This module contains functions for initializing, updating, and managing
database tables with DataFrame data.
"""

import json
import pandas as pd
from typing import Any, List
from sqlalchemy.exc import IntegrityError
from sqlalchemy import func
from utils import get_logger
from app import db


def initialize_db_table(df: pd.DataFrame, Model: Any, key_cols: List[str]) -> bool:
    """
    Initialize a database table with DataFrame data.
    
    Args:
        df: DataFrame with data
        Model: SQLAlchemy model class (e.g., Project, Folder)
        key_cols: Columns forming unique constraint
        
    Returns:
        Success status
    """
    if df.empty:
        return False

    # Convert dict/list columns to JSON strings and handle NaN values
    df = _prepare_dataframe_for_db(df)

    try:
        # Get model column names once for efficiency
        model_columns = [c.name for c in Model.__table__.columns]
        
        # Debug logging for folder name issues
        logger = get_logger()
        if Model.__tablename__ == 'folders':
            logger.info(f"Folder columns in DataFrame: {df.columns.tolist()}")
            if 'name' in df.columns:
                logger.info(f"Sample folder names: {df['name'].head(5).tolist()}")
            else:
                logger.warning(f"'name' column missing from folders DataFrame")
        
        # Process in batches for efficiency
        batch_size = 100
        for i in range(0, len(df), batch_size):
            batch_df = df.iloc[i:i+batch_size]
            for _, row in batch_df.iterrows():
                # Ensure row matches model columns
                row_dict = {k: v for k, v in row.to_dict().items() if k in model_columns}
                
                # Check if record already exists by primary key using key_cols parameter
                filter_dict = {col: row_dict[col] for col in key_cols if col in row_dict}
                if filter_dict:
                    # Try to find the record by key columns
                    existing = db.session.query(Model).filter_by(**filter_dict).first()
                    
                    if existing:
                        # Update existing record with new values
                        for key, value in row_dict.items():
                            setattr(existing, key, value)
                    else:
                        # Create new record
                        record = Model(**row_dict)
                        db.session.add(record)
                else:
                    # No key columns found, just add as new record
                    record = Model(**row_dict)
                    db.session.add(record)
            
            # Commit each batch
            db.session.commit()
            
        return True
    except IntegrityError as e:
        db.session.rollback()
        logger = get_logger()
        logger.error(f"Table init failed: {e}")
        return False


def append_after_latest(df: pd.DataFrame, Model: Any, timestamp_col: str) -> int:
    """
    Append new records based on timestamp.
    
    Args:
        df: DataFrame with data
        Model: SQLAlchemy model
        timestamp_col: Column for timestamp comparison
        
    Returns:
        Number of records added
    """
    if df.empty:
        return 0

    last_timestamp = db.session.query(func.max(getattr(Model, timestamp_col))).scalar()
    df_new = df[df[timestamp_col] > last_timestamp] if last_timestamp else df

    if df_new.empty:
        return 0

    df_new = _prepare_dataframe_for_db(df_new)

    records_added = 0
    try:
        model_columns = [c.name for c in Model.__table__.columns]
        for _, row in df_new.iterrows():
            row_dict = {k: v for k, v in row.to_dict().items() if k in model_columns}
            record = Model(**row_dict)
            db.session.merge(record)
            records_added += 1
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        logger = get_logger()
        logger.error(f"Append error: {e}")
    return records_added


def insert_unique(df: pd.DataFrame, Model: Any) -> int:
    """
    Insert unique records into table.
    
    Args:
        df: DataFrame with data
        Model: SQLAlchemy model
        
    Returns:
        Number of records added
    """
    if df.empty:
        return 0

    df = _prepare_dataframe_for_db(df)

    records_added = 0
    try:
        model_columns = [c.name for c in Model.__table__.columns]
        for _, row in df.iterrows():
            row_dict = {k: v for k, v in row.to_dict().items() if k in model_columns}
            record = Model(**row_dict)
            db.session.merge(record)
            records_added += 1
        db.session.commit()
    except Exception as e:
        db.session.rollback()
        logger = get_logger()
        logger.error(f"Insert error: {e}")
    return records_added


def _prepare_dataframe_for_db(df: pd.DataFrame) -> pd.DataFrame:
    """
    Prepare DataFrame for database insertion by converting complex types and handling NaN values.
    
    Args:
        df: Input DataFrame
        
    Returns:
        Prepared DataFrame
    """
    # Make a copy to avoid modifying the original
    df_copy = df.copy()
    
    for col in df_copy.columns:
        # Convert dict/list to JSON strings
        if df_copy[col].apply(lambda x: isinstance(x, (dict, list))).any():
            df_copy[col] = df_copy[col].apply(json.dumps)
        
        # Replace NaN values with None (will become NULL in SQL)
        if df_copy[col].isna().any():
            df_copy[col] = df_copy[col].where(pd.notna(df_copy[col]), None)
    
    return df_copy
