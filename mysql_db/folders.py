"""
Folder-specific operations for Filevine data management.

This module contains functions and classes for managing folder data
and folder-related operations.
"""

import pandas as pd
from typing import List, Optional, Any, Tuple
from datetime import datetime
from utils import get_logger
from mysql_db.models import Folder, LogEntry
from mysql_db.table_operations import initialize_db_table
from app import db


class FolderManager:
    """Manager class for folder-related operations."""
    
    def __init__(self):
        self.logger = get_logger()
    
    def get_folders_by_project(self, project_id: int) -> List[Folder]:
        """
        Get all folders for a specific project.
        
        Args:
            project_id: The native ID of the project
            
        Returns:
            List of Folder objects
        """
        try:
            return db.session.query(Folder).filter_by(project_id_native=project_id).all()
        except Exception as e:
            self.logger.error(f"Error fetching folders for project {project_id}: {e}")
            return []
    
    def get_folder_by_id(self, folder_id: int) -> Optional[Folder]:
        """
        Get a folder by its native ID.
        
        Args:
            folder_id: The native ID of the folder
            
        Returns:
            Folder object or None if not found
        """
        try:
            return db.session.query(Folder).filter_by(native_id=folder_id).first()
        except Exception as e:
            self.logger.error(f"Error fetching folder {folder_id}: {e}")
            return None
    
    def update_folders_table(self, df: pd.DataFrame) -> bool:
        """
        Update the folders table with new data.
        
        Args:
            df: DataFrame containing folder data
            
        Returns:
            Success status
        """
        if df.empty:
            self.logger.warning("Empty DataFrame provided for folders update")
            return False
        
        try:
            success = initialize_db_table(df, Folder, ['native_id'])
            if success:
                self.logger.info(f"Successfully updated {len(df)} folders")
            else:
                self.logger.error("Failed to update folders table")
            return success
        except Exception as e:
            self.logger.error(f"Error updating folders table: {e}")
            return False
    
    def get_folders_summary(self) -> dict:
        """
        Get a summary of folders in the database.
        
        Returns:
            Dictionary with folder statistics
        """
        try:
            total_folders = db.session.query(Folder).count()
            archived_folders = db.session.query(Folder).filter_by(is_archived=True).count()
            root_folders = db.session.query(Folder).filter_by(is_root_folder=True).count()
            active_folders = total_folders - archived_folders
            
            return {
                'total_folders': total_folders,
                'active_folders': active_folders,
                'archived_folders': archived_folders,
                'root_folders': root_folders
            }
        except Exception as e:
            self.logger.error(f"Error getting folders summary: {e}")
            return {
                'total_folders': 0,
                'active_folders': 0,
                'archived_folders': 0,
                'root_folders': 0
            }


async def update_folders(session: Any, batch_size: int = 1000, max_records: Optional[int] = None, resume_offset: int = 0) -> Tuple[bool, int]:
    """
    Update folders table with data from Filevine API with batch upsert for reliability.
    Implements batch processing with database commits after each batch to prevent data loss.
    
    Args:
        session: Authenticated FilevineSession
        batch_size: Number of records to process in each batch
        max_records: Maximum number of records to process (None for all)
        resume_offset: Optional offset to resume from after a previous failure
        
    Returns:
        Tuple containing (success status, last successfully processed offset)
    """
    # Initialize imports to avoid circular imports
    from mysql_db.FV_db import _init_imports
    _init_imports()
    
    # Import paginator after initialization
    from filevine.FV_API import FolderPaginator
    
    logger = get_logger()
    logger.info(f"Starting folder update task with batch size {batch_size}")
    
    # Track statistics for reporting
    total_folders = 0
    current_offset = resume_offset
    last_success_offset = resume_offset
    
    try:
        # If resuming, log that information
        if resume_offset > 0:
            logger.info(f"Resuming folder update from offset {resume_offset}")
        
        # Create a paginator that will handle batch retrieval
        paginator = FolderPaginator(session, offset=str(current_offset), limit=str(batch_size))
        
        # Process folders in batches
        async for folder_batch_df in paginator.async_paginate(paginator.async_fetch, max_records, return_chunks=True):
            if folder_batch_df.empty:
                logger.warning(f"Empty batch at offset {current_offset}, stopping")
                break
                
            batch_size_actual = len(folder_batch_df)
            logger.info(f"Processing batch of {batch_size_actual} folders at offset {current_offset}")
            
            try:
                # Update folders table with this batch
                success = initialize_db_table(folder_batch_df, Folder, ['native_id'])
                
                if success:
                    # Update statistics and last successful offset
                    total_folders += batch_size_actual
                    last_success_offset = current_offset
                    logger.info(f"Successfully upserted {batch_size_actual} folders at offset {current_offset}")
                else:
                    logger.error(f"Failed to upsert folders batch at offset {current_offset}")
                    # Log the failure for tracking
                    log_entry = LogEntry(
                        timestamp=datetime.utcnow(),
                        level="ERROR",
                        message=f"Failed to upsert folders batch",
                        context={
                            "offset": current_offset,
                            "batch_size": batch_size_actual,
                            "task": "update_folders"
                        }
                    )
                    db.session.add(log_entry)
                    db.session.commit()
            except Exception as batch_error:
                logger.error(f"Error processing folders batch at offset {current_offset}: {batch_error}")
                # Log the exception
                log_entry = LogEntry(
                    timestamp=datetime.utcnow(),
                    level="ERROR",
                    message=f"Exception processing folders batch: {str(batch_error)}",
                    context={
                        "offset": current_offset,
                        "batch_size": batch_size_actual,
                        "task": "update_folders",
                        "exception": str(batch_error)
                    }
                )
                db.session.add(log_entry)
                db.session.commit()
            
            # Clear the DataFrame to free memory before processing next batch
            del folder_batch_df
            
            # Update offset for next iteration
            current_offset += batch_size_actual
        
        # Final report
        logger.info(f"Folder update completed: processed {total_folders} folders, last successful offset: {last_success_offset}")
        
        # Return success status and the last successful offset for possible resume
        return total_folders > 0, last_success_offset
    
    except Exception as e:
        logger.error(f"Error updating folders: {e}")
        # Log the failure for tracking/querying on next run
        log_entry = LogEntry(
            timestamp=datetime.utcnow(),
            level="ERROR",
            message=f"Failed to complete folder update task: {str(e)}",
            context={
                "last_success_offset": last_success_offset,
                "total_processed": total_folders,
                "task": "update_folders",
                "exception": str(e)
            }
        )
        try:
            db.session.add(log_entry)
            db.session.commit()
        except Exception as log_error:
            logger.error(f"Could not log folder update failure: {log_error}")
        
        return False, last_success_offset
