import asyncio
import json
from filevine.FV_API import FilevineSession, FilePaginator

PROJECT_IDS_FILE = "test_projectIds.txt"
TOTAL_DOCS = 10000
BATCH_SIZE = 1000  # per project fetch, to avoid API overload

async def fetch_documents_for_projects(session, project_ids, outfile, max_docs=5000):
    all_items = []
    for pid in project_ids:
        paginator = FilePaginator(session, offset="0", limit=str(BATCH_SIZE), projectID=str(pid))
        async for chunk_df in paginator.async_paginate(paginator.async_fetch, max_records=BATCH_SIZE, return_chunks=True):
            all_items.extend(chunk_df.to_dict(orient="records"))
            if len(all_items) >= max_docs:
                break
        if len(all_items) >= max_docs:
            break
    with open(outfile, 'w') as f:
        json.dump(all_items[:max_docs], f, indent=2)
    print(f"Saved {len(all_items[:max_docs])} records to {outfile}")
    return all_items[:max_docs]

async def main():
    # Read project IDs
    with open(PROJECT_IDS_FILE) as f:
        project_ids = [line.strip() for line in f if line.strip()]
    n = len(project_ids)
    if n < 2:
        print("Not enough project IDs in file.")
        return
    session = FilevineSession()
    # Split project IDs for diversity: half from start, half from end
    half = TOTAL_DOCS // 2 // BATCH_SIZE
    old_projects = project_ids[:half]
    new_projects = project_ids[-half:]
    print(f"Fetching up to {TOTAL_DOCS//2} docs from oldest projects...")
    await fetch_documents_for_projects(session, old_projects, "documents_old_projects.json", max_docs=TOTAL_DOCS//2)
    print(f"Fetching up to {TOTAL_DOCS//2} docs from newest projects...")
    await fetch_documents_for_projects(session, new_projects, "documents_new_projects.json", max_docs=TOTAL_DOCS//2)
    print("Done.")

if __name__ == "__main__":
    asyncio.run(main())
