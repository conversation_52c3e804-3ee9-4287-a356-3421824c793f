# Database Schema Reference

This document serves as the definitive reference for all database table fields in the BLS_Backups system. It must be consulted before making any changes to database operations or parsing logic.

## Rule
**IMPORTANT**: These field lists are the ONLY authoritative reference for database operations and data parsing. All code that interacts with the database MUST align with these definitions to prevent field mismatches. Before modifying any database operations or API response parsing, consult this document to ensure alignment between ORM models, field mappings, and database schema.

## Table: projects
| Column Name | Data Type |
|-------------|-----------|
| id | int |
| native_id | int |
| partner_id | varchar(255) |
| name | varchar(255) |
| project_name | varchar(255) |
| client_name | varchar(255) |
| created_date | varchar(255) |
| status | varchar(100) |
| is_archived | tinyint(1) |
| first_primary_name | varchar(255) |
| first_primary_username | varchar(255) |
| project_or_client_name | varchar(255) |
| phase_name | varchar(255) |
| org_id | int |
| unique_key | varchar(255) |
| project_url | varchar(255) |
| last_activity | varchar(255) |
| project_email_address | varchar(255) |
| incident_date | varchar(255) |
| number | varchar(100) |
| client_id_native | int |
| client_id_partner | varchar(255) |
| project_type_id_native | int |
| project_type_id_partner | varchar(255) |
| phase_id_native | int |
| phase_id_partner | varchar(255) |
| root_doc_folder_id_native | int |
| root_doc_folder_id_partner | varchar(255) |
| hashtags | json |
| metadata | json |

## Table: folders
| Column Name | Data Type |
|-------------|-----------|
| id | int |
| native_id | int |
| partner_id | varchar(255) |
| parent_id_native | int |
| parent_id_partner | varchar(255) |
| project_id_native | int |
| project_id_partner | varchar(255) |
| name | varchar(255) |
| path | varchar(1000) |
| is_archived | tinyint(1) |
| is_root_folder | tinyint(1) |
| created_date | varchar(255) |
| hashtags | json |
| meta_data | json |

## Table: documents
| Column Name | Data Type |
|-------------|-----------|
| id | int |
| native_id | int |
| partner_id | varchar(255) |
| folder_id_native | int |
| folder_id_partner | varchar(255) |
| project_id_native | int |
| project_id_partner | varchar(255) |
| name | varchar(500) |
| original_filename | varchar(500) |
| path | varchar(1000) |
| is_archived | tinyint(1) |
| ocr_data | text |
| upload_date | varchar(255) |
| file_size | int |
| content_type | varchar(100) |
| hash | varchar(255) |
| download_url | varchar(2000) |
| download_status | varchar(50) |
| download_started_at | datetime |
| download_completed_at | datetime |
| download_error | text |
| download_attempts | int |
| version_key | varchar(255) |
| hashtags | json |
| links | json |

## Table: logs
| Column Name | Data Type |
|-------------|-----------|
| id | int |
| timestamp | datetime |
| level | varchar(20) |
| message | text |
| project_id | int |
| context | json |
